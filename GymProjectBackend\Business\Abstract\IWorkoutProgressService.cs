/// Workout Progress Service Interface - GymKod Pro Backend
/// 
/// Bu interface antrenman ilerleme sistemi için business logic operasyonlarını tanımlar.
using Core.Utilities.Results;
using Entities.DTOs;

namespace Business.Abstract
{
    /// <summary>
    /// Workout Progress Service Interface
    /// </summary>
    public interface IWorkoutProgressService
    {
        /// <summary>
        /// Antrenman oturumu başlatır veya mevcut oturumu getirir
        /// </summary>
        IDataResult<StartWorkoutSessionResponseDto> StartWorkoutSession(StartWorkoutSessionDto startDto);

        /// <summary>
        /// Egzersiz tamamlama durumunu toggle eder
        /// </summary>
        IDataResult<ExerciseCompletionResponseDto> ToggleExerciseCompletion(ToggleExerciseCompletionDto toggleDto);

        /// <summary>
        /// Kullanıcının bugünkü aktif antrenman oturumunu getirir
        /// </summary>
        IDataResult<MobileWorkoutSessionDto> GetTodayWorkoutSession(int userId, int workoutProgramDayId);

        /// <summary>
        /// Antrenman oturumu detaylarını getirir
        /// </summary>
        IDataResult<MobileWorkoutSessionDto> GetWorkoutSessionDetail(int workoutSessionId);

        /// <summary>
        /// Kullanıcının antrenman geçmişini getirir
        /// </summary>
        IDataResult<List<WorkoutSessionStatsDto>> GetWorkoutHistory(int userId, int? limit = null);

        /// <summary>
        /// Kullanıcının egzersiz istatistiklerini getirir
        /// </summary>
        IDataResult<List<ExerciseStatsDto>> GetExerciseStats(int userId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// Kullanıcının haftalık antrenman istatistiklerini getirir
        /// </summary>
        IDataResult<List<WorkoutSessionStatsDto>> GetWeeklyStats(int userId, DateTime? weekStartDate = null);

        /// <summary>
        /// Antrenman oturumunu tamamlar
        /// </summary>
        IResult CompleteWorkoutSession(int workoutSessionId);

        /// <summary>
        /// Antrenman oturumunu iptal eder
        /// </summary>
        IResult CancelWorkoutSession(int workoutSessionId);
    }
}
