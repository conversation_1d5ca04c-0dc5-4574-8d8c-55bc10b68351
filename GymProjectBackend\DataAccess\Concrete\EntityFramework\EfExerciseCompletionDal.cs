/// Exercise Completion Entity Framework Data Access - GymKod Pro Backend
/// 
/// Bu class ExerciseCompletion entity'si için Entity Framework data access implementasyonunu sağlar.
using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// Exercise Completion Entity Framework Data Access
    /// </summary>
    public class EfExerciseCompletionDal : EfEntityRepositoryBase<ExerciseCompletion, GymContext>, IExerciseCompletionDal
    {
        public List<ExerciseCompletion> GetByWorkoutSession(int workoutSessionId)
        {
            using var context = new GymContext();
            return context.ExerciseCompletions
                .Where(ec => ec.WorkoutSessionID == workoutSessionId)
                .Include(ec => ec.WorkoutProgramExercise)
                .OrderBy(ec => ec.WorkoutProgramExercise!.OrderIndex)
                .ToList();
        }

        public ExerciseCompletion? GetByWorkoutSessionAndExercise(int workoutSessionId, int workoutProgramExerciseId)
        {
            using var context = new GymContext();
            return context.ExerciseCompletions
                .FirstOrDefault(ec => ec.WorkoutSessionID == workoutSessionId
                    && ec.WorkoutProgramExerciseID == workoutProgramExerciseId);
        }

        public bool ToggleExerciseCompletion(int workoutSessionId, int workoutProgramExerciseId, bool isCompleted)
        {
            using var context = new GymContext();
            using var transaction = context.Database.BeginTransaction();

            try
            {
                // Egzersizin toplam set sayısını al
                var exercise = context.WorkoutProgramExercises
                    .FirstOrDefault(wpe => wpe.WorkoutProgramExerciseID == workoutProgramExerciseId);

                if (exercise == null)
                    return false;

                // Mevcut completion kaydını kontrol et
                var existingCompletion = context.ExerciseCompletions
                    .FirstOrDefault(ec => ec.WorkoutSessionID == workoutSessionId 
                        && ec.WorkoutProgramExerciseID == workoutProgramExerciseId);

                if (existingCompletion == null)
                {
                    // Yeni kayıt oluştur
                    var newCompletion = new ExerciseCompletion
                    {
                        WorkoutSessionID = workoutSessionId,
                        WorkoutProgramExerciseID = workoutProgramExerciseId,
                        IsCompleted = isCompleted,
                        CompletedSets = isCompleted ? exercise.Sets : 0,
                        TotalSets = exercise.Sets,
                        CompletionPercentage = isCompleted ? 100.00m : 0.00m,
                        CompletedAt = isCompleted ? DateTime.Now : null,
                        CreatedDate = DateTime.Now
                    };

                    context.ExerciseCompletions.Add(newCompletion);
                }
                else
                {
                    // Mevcut kaydı güncelle
                    existingCompletion.IsCompleted = isCompleted;
                    existingCompletion.CompletedSets = isCompleted ? exercise.Sets : 0;
                    existingCompletion.CompletionPercentage = isCompleted ? 100.00m : 0.00m;
                    existingCompletion.CompletedAt = isCompleted ? DateTime.Now : null;
                    existingCompletion.UpdatedDate = DateTime.Now;
                }

                // Workout session'ı güncelle
                var workoutSession = context.WorkoutSessions
                    .FirstOrDefault(ws => ws.WorkoutSessionID == workoutSessionId);

                if (workoutSession != null)
                {
                    var completedCount = context.ExerciseCompletions
                        .Count(ec => ec.WorkoutSessionID == workoutSessionId && ec.IsCompleted);

                    // Yeni eklenen/güncellenen kaydı da hesaba kat
                    if (isCompleted && existingCompletion == null)
                        completedCount++;
                    else if (!isCompleted && existingCompletion?.IsCompleted == true)
                        completedCount--;

                    workoutSession.CompletedExercises = completedCount;
                    workoutSession.UpdateCompletionPercentage();
                    workoutSession.UpdatedDate = DateTime.Now;

                    // Tüm egzersizler tamamlandıysa antrenmanı bitir
                    if (completedCount >= workoutSession.TotalExercises)
                    {
                        workoutSession.CompleteWorkout();
                    }
                    else if (workoutSession.IsCompleted)
                    {
                        // Antrenman tamamlanmışsa ama egzersiz geri alındıysa
                        workoutSession.IsCompleted = false;
                        workoutSession.EndTime = null;
                    }
                }

                context.SaveChanges();
                transaction.Commit();
                return true;
            }
            catch
            {
                transaction.Rollback();
                return false;
            }
        }

        public List<ExerciseCompletionHistoryDto> GetMemberExerciseHistory(int memberId, int? exerciseId = null, int? limit = null)
        {
            using var context = new GymContext();

            var query = context.ExerciseCompletions
                .Include(ec => ec.WorkoutSession)
                .Include(ec => ec.WorkoutProgramExercise)
                .Where(ec => ec.WorkoutSession!.MemberID == memberId && ec.IsCompleted);

            if (exerciseId.HasValue)
            {
                query = query.Where(ec => ec.WorkoutProgramExerciseID == exerciseId.Value);
            }

            query = query.OrderByDescending(ec => ec.CompletedAt);

            if (limit.HasValue)
            {
                query = query.Take(limit.Value);
            }

            return query.Select(ec => new ExerciseCompletionHistoryDto
            {
                ExerciseCompletionID = ec.ExerciseCompletionID,
                ExerciseName = ec.WorkoutProgramExercise!.ExerciseName,
                CompletedAt = ec.CompletedAt,
                CompletedSets = ec.CompletedSets,
                TotalSets = ec.TotalSets,
                CompletionPercentage = ec.CompletionPercentage,
                SessionDate = ec.WorkoutSession!.SessionDate
            }).ToList();
        }

        public List<ExerciseStatsDto> GetExerciseStats(int memberId, DateTime? startDate = null, DateTime? endDate = null)
        {
            using var context = new GymContext();

            var query = context.ExerciseCompletions
                .Include(ec => ec.WorkoutSession)
                .Include(ec => ec.WorkoutProgramExercise)
                .Where(ec => ec.WorkoutSession!.MemberID == memberId && ec.IsCompleted);

            if (startDate.HasValue)
                query = query.Where(ec => ec.WorkoutSession!.SessionDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(ec => ec.WorkoutSession!.SessionDate <= endDate.Value);

            return query
                .GroupBy(ec => new { ec.WorkoutProgramExerciseID, ec.WorkoutProgramExercise!.ExerciseName })
                .Select(g => new ExerciseStatsDto
                {
                    ExerciseID = g.Key.WorkoutProgramExerciseID,
                    ExerciseName = g.Key.ExerciseName,
                    TotalCompletions = g.Count(),
                    AverageCompletionPercentage = g.Average(ec => ec.CompletionPercentage),
                    LastCompletedAt = g.Max(ec => ec.CompletedAt)
                })
                .OrderByDescending(es => es.TotalCompletions)
                .ToList();
        }

        public List<ExerciseStatsDto> GetMostCompletedExercises(int memberId, int limit = 10)
        {
            using var context = new GymContext();

            return context.ExerciseCompletions
                .Include(ec => ec.WorkoutSession)
                .Include(ec => ec.WorkoutProgramExercise)
                .Where(ec => ec.WorkoutSession!.MemberID == memberId && ec.IsCompleted)
                .GroupBy(ec => new { ec.WorkoutProgramExerciseID, ec.WorkoutProgramExercise!.ExerciseName })
                .Select(g => new ExerciseStatsDto
                {
                    ExerciseID = g.Key.WorkoutProgramExerciseID,
                    ExerciseName = g.Key.ExerciseName,
                    TotalCompletions = g.Count(),
                    AverageCompletionPercentage = g.Average(ec => ec.CompletionPercentage),
                    LastCompletedAt = g.Max(ec => ec.CompletedAt)
                })
                .OrderByDescending(es => es.TotalCompletions)
                .Take(limit)
                .ToList();
        }
    }
}
