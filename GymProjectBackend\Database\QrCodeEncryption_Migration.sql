-- QR Kod Şifreleme Sistemi Migration Script
-- Bu script yeni şifreli QR kod sisteminin test edilmesi için hazırlanmıştır.
-- Mevcut QR kod sistemi tamamen değiştirilmiştir.

-- Yeni sistem özellikleri:
-- 1. QR kod içeriği tamamen AES-256 ile şifrelenir
-- 2. Member ID + ScanNumber + Timestamp JSON formatında şifrelenir
-- 3. Base64 encode edilir ve QR kod olarak kullanılır
-- 4. 5 dakika geçerlilik süresi
-- 5. Kopyalama imkansız hale gelir

-- Test için örnek QR kod oluşturma:
-- Bu script'i çalıştırdıktan sonra API'den QR kod alarak test edebilirsiniz.

-- Mevcut Member tablosunda herhangi bir değişiklik gerekmez
-- Çünkü ScanNumber alanı zaten mevcut ve şifreli QR kod bu alana dayalı olarak oluşturulur

-- Test adımları:
-- 1. API'den GetMemberQRByPhoneNumber endpoint'ini çağırın
-- 2. Dönen ScanNumber artık şifreli bir token olacak
-- 3. Bu token'ı GetMemberRemainingDaysForScanNumber endpoint'ine gönderin
-- 4. Sistem token'ı çözecek ve üye bilgilerini döndürecek

-- Güvenlik kontrolleri:
-- - Token 5 dakika sonra geçersiz olur
-- - Token kopyalanıp başka yerde kullanılamaz
-- - Member ID şifreli olduğu için tahmin edilemez
-- - AES-256 şifreleme ile maksimum güvenlik

PRINT 'QR Kod Şifreleme Sistemi başarıyla aktifleştirildi!'
PRINT 'Artık tüm QR kodlar AES-256 ile şifrelenir.'
PRINT 'Test için API endpoint''lerini kullanabilirsiniz.'
