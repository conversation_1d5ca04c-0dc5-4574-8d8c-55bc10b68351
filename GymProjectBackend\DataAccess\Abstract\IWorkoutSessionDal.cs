/// Workout Session Data Access Interface - GymKod Pro Backend
/// 
/// Bu interface WorkoutSession entity'si için data access operasyonlarını tanımlar.
using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;

namespace DataAccess.Abstract
{
    /// <summary>
    /// Workout Session Data Access Interface
    /// </summary>
    public interface IWorkoutSessionDal : IEntityRepository<WorkoutSession>
    {
        /// <summary>
        /// Üyenin bugünkü aktif antrenman oturumunu getirir
        /// </summary>
        WorkoutSession? GetTodayActiveSession(int memberId, int workoutProgramDayId);

        /// <summary>
        /// Üyenin belirli tarihteki antrenman oturumunu getirir
        /// </summary>
        WorkoutSession? GetSessionByDate(int memberId, int workoutProgramDayId, DateTime sessionDate);

        /// <summary>
        /// Üyenin antrenman geçmişini getirir
        /// </summary>
        List<WorkoutSession> GetMemberWorkoutHistory(int memberId, int? limit = null);

        /// <summary>
        /// Antrenman oturumu başlatır veya mevcut oturumu getirir
        /// </summary>
        WorkoutSession StartOrGetWorkoutSession(int memberId, int workoutProgramTemplateId, int workoutProgramDayId);

        /// <summary>
        /// Antrenman oturumu istatistiklerini getirir
        /// </summary>
        WorkoutSessionStatsDto GetWorkoutSessionStats(int workoutSessionId);

        /// <summary>
        /// Üyenin haftalık antrenman istatistiklerini getirir
        /// </summary>
        List<WorkoutSessionStatsDto> GetWeeklyStats(int memberId, DateTime weekStartDate);

        /// <summary>
        /// Şirket bazlı günlük antrenman istatistiklerini getirir
        /// </summary>
        List<WorkoutSessionStatsDto> GetCompanyDailyStats(int companyId, DateTime date);
    }
}
