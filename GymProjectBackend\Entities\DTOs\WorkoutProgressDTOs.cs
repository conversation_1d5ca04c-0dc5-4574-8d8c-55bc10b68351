/// Workout Progress DTOs - GymKod Pro Backend
/// 
/// Bu dosya antrenman ilerleme sistemi için gerekli DTO'ları içerir.
using Core.Entities;

namespace Entities.DTOs
{
    /// <summary>
    /// Antrenman oturumu başlatma DTO'su
    /// </summary>
    public class StartWorkoutSessionDto : IDto
    {
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int WorkoutProgramDayID { get; set; }
    }

    /// <summary>
    /// Egzersiz tamamlama toggle DTO'su
    /// </summary>
    public class ToggleExerciseCompletionDto : IDto
    {
        public int WorkoutSessionID { get; set; }
        public int WorkoutProgramExerciseID { get; set; }
        public bool IsCompleted { get; set; }
    }

    /// <summary>
    /// Antrenman oturumu istatistikleri DTO'su
    /// </summary>
    public class WorkoutSessionStatsDto : IDto
    {
        public int WorkoutSessionID { get; set; }
        public DateTime SessionDate { get; set; }
        public int TotalExercises { get; set; }
        public int CompletedExercises { get; set; }
        public decimal CompletionPercentage { get; set; }
        public int? WorkoutDurationMinutes { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// Egzersiz tamamlama geçmişi DTO'su
    /// </summary>
    public class ExerciseCompletionHistoryDto : IDto
    {
        public int ExerciseCompletionID { get; set; }
        public string ExerciseName { get; set; } = string.Empty;
        public DateTime? CompletedAt { get; set; }
        public int CompletedSets { get; set; }
        public int TotalSets { get; set; }
        public decimal CompletionPercentage { get; set; }
        public DateTime SessionDate { get; set; }
    }

    /// <summary>
    /// Egzersiz istatistikleri DTO'su
    /// </summary>
    public class ExerciseStatsDto : IDto
    {
        public int ExerciseID { get; set; }
        public string ExerciseName { get; set; } = string.Empty;
        public int TotalCompletions { get; set; }
        public decimal AverageCompletionPercentage { get; set; }
        public DateTime? LastCompletedAt { get; set; }
    }

    /// <summary>
    /// Mobil API için antrenman oturumu detay DTO'su
    /// </summary>
    public class MobileWorkoutSessionDto : IDto
    {
        public int WorkoutSessionID { get; set; }
        public int WorkoutProgramDayID { get; set; }
        public string DayName { get; set; } = string.Empty;
        public DateTime SessionDate { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsCompleted { get; set; }
        public decimal CompletionPercentage { get; set; }
        public int TotalExercises { get; set; }
        public int CompletedExercises { get; set; }
        public List<MobileExerciseCompletionDto> ExerciseCompletions { get; set; } = new();
    }

    /// <summary>
    /// Mobil API için egzersiz tamamlama DTO'su
    /// </summary>
    public class MobileExerciseCompletionDto : IDto
    {
        public int WorkoutProgramExerciseID { get; set; }
        public string ExerciseName { get; set; } = string.Empty;
        public int Sets { get; set; }
        public string Reps { get; set; } = string.Empty;
        public bool IsCompleted { get; set; }
        public int CompletedSets { get; set; }
        public decimal CompletionPercentage { get; set; }
        public DateTime? CompletedAt { get; set; }
    }

    /// <summary>
    /// Egzersiz tamamlama yanıt DTO'su
    /// </summary>
    public class ExerciseCompletionResponseDto : IDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public MobileExerciseCompletionDto? ExerciseCompletion { get; set; }
        public WorkoutSessionStatsDto? WorkoutSessionStats { get; set; }
    }

    /// <summary>
    /// Antrenman oturumu başlatma yanıt DTO'su
    /// </summary>
    public class StartWorkoutSessionResponseDto : IDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public MobileWorkoutSessionDto? WorkoutSession { get; set; }
    }
}
