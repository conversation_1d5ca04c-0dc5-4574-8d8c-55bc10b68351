/// Workout Progress Provider - GymKod Pro Mobile
/// 
/// Bu provider antrenman ilerleme takibi state management'ını yönetir.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/services/logging_service.dart';
import '../../../../core/services/api_service.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../../data/models/workout_progress_models.dart';
import '../../data/services/workout_progress_api_service.dart';

/// Workout Progress State
class WorkoutProgressState {
  final MobileWorkoutSessionDto? currentSession;
  final List<WorkoutSessionStatsDto> workoutHistory;
  final List<ExerciseStatsDto> exerciseStats;
  final bool isLoading;
  final bool isToggling;
  final String? error;
  final DateTime? lastRefreshTime;

  const WorkoutProgressState({
    this.currentSession,
    this.workoutHistory = const [],
    this.exerciseStats = const [],
    this.isLoading = false,
    this.isToggling = false,
    this.error,
    this.lastRefreshTime,
  });

  /// Hata var mı
  bool get hasError => error != null;

  /// Aktif oturum var mı
  bool get hasActiveSession => currentSession != null;

  /// Bugünkü antrenman tamamlandı mı
  bool get isTodayWorkoutCompleted => currentSession?.isCompleted ?? false;

  /// Yenile yapılabilir mi (10 saniye kuralı)
  bool get canRefreshNow {
    if (lastRefreshTime == null) return true;
    return DateTime.now().difference(lastRefreshTime!).inSeconds >= 10;
  }

  /// Yenilemeye kalan saniye
  int get remainingSecondsToRefresh {
    if (lastRefreshTime == null) return 0;
    final elapsed = DateTime.now().difference(lastRefreshTime!).inSeconds;
    return (10 - elapsed).clamp(0, 10);
  }

  WorkoutProgressState copyWith({
    MobileWorkoutSessionDto? currentSession,
    List<WorkoutSessionStatsDto>? workoutHistory,
    List<ExerciseStatsDto>? exerciseStats,
    bool? isLoading,
    bool? isToggling,
    String? error,
    DateTime? lastRefreshTime,
    bool clearError = false,
    bool clearCurrentSession = false,
  }) {
    return WorkoutProgressState(
      currentSession: clearCurrentSession ? null : (currentSession ?? this.currentSession),
      workoutHistory: workoutHistory ?? this.workoutHistory,
      exerciseStats: exerciseStats ?? this.exerciseStats,
      isLoading: isLoading ?? this.isLoading,
      isToggling: isToggling ?? this.isToggling,
      error: clearError ? null : (error ?? this.error),
      lastRefreshTime: lastRefreshTime ?? this.lastRefreshTime,
    );
  }

  @override
  String toString() {
    return 'WorkoutProgressState(hasSession: $hasActiveSession, isLoading: $isLoading, hasError: $hasError)';
  }
}

/// Workout Progress State Notifier
class WorkoutProgressNotifier extends StateNotifier<WorkoutProgressState> {
  final WorkoutProgressApiService _apiService;
  final Ref _ref;

  WorkoutProgressNotifier(this._apiService, this._ref) : super(const WorkoutProgressState());

  /// Antrenman oturumu başlat veya mevcut oturumu getir
  Future<void> startOrGetWorkoutSession(int memberID, int workoutProgramTemplateID, int workoutProgramDayID) async {
    try {
      LoggingService.stateLog('WorkoutProgressNotifier', 'Starting workout session', 
        state: 'memberID: $memberID, dayID: $workoutProgramDayID');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        lastRefreshTime: DateTime.now(),
      );

      final startDto = StartWorkoutSessionDto(
        memberID: memberID,
        workoutProgramTemplateID: workoutProgramTemplateID,
        workoutProgramDayID: workoutProgramDayID,
      );

      final result = await _apiService.startWorkoutSession(startDto);

      if (result.success && result.data != null) {
        state = state.copyWith(
          currentSession: result.data,
          isLoading: false,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Workout session started successfully', 
          state: 'sessionID: ${result.data!.workoutSessionID}');
      } else {
        state = state.copyWith(
          error: result.message,
          isLoading: false,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Failed to start workout session', 
          state: result.message);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressNotifier.startOrGetWorkoutSession');
      
      state = state.copyWith(
        error: 'Antrenman oturumu başlatılırken hata oluştu: $e',
        isLoading: false,
      );
    }
  }

  /// Egzersiz tamamlama durumunu toggle et
  Future<void> toggleExerciseCompletion(int workoutProgramExerciseID, bool isCompleted) async {
    if (state.currentSession == null) {
      LoggingService.stateLog('WorkoutProgressNotifier', 'No active session for exercise toggle', 
        state: 'exerciseID: $workoutProgramExerciseID');
      return;
    }

    try {
      LoggingService.stateLog('WorkoutProgressNotifier', 'Toggling exercise completion', 
        state: 'exerciseID: $workoutProgramExerciseID, completed: $isCompleted');

      state = state.copyWith(
        isToggling: true,
        clearError: true,
      );

      final toggleDto = ToggleExerciseCompletionDto(
        workoutSessionID: state.currentSession!.workoutSessionID,
        workoutProgramExerciseID: workoutProgramExerciseID,
        isCompleted: isCompleted,
      );

      final result = await _apiService.toggleExerciseCompletion(toggleDto);

      if (result.success && result.data != null) {
        // Mevcut session'ı güncelle
        final updatedSession = _updateSessionWithExerciseCompletion(
          state.currentSession!,
          result.data!.exerciseCompletion!,
          result.data!.workoutSessionStats!,
        );

        state = state.copyWith(
          currentSession: updatedSession,
          isToggling: false,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Exercise completion toggled successfully', 
          state: 'exerciseID: $workoutProgramExerciseID, newStatus: $isCompleted');
      } else {
        state = state.copyWith(
          error: result.message,
          isToggling: false,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Failed to toggle exercise completion', 
          state: result.message);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressNotifier.toggleExerciseCompletion');
      
      state = state.copyWith(
        error: 'Egzersiz tamamlama durumu güncellenirken hata oluştu: $e',
        isToggling: false,
      );
    }
  }

  /// Bugünkü antrenman oturumunu getir
  Future<void> getTodayWorkoutSession(int userId, int workoutProgramDayId) async {
    try {
      LoggingService.stateLog('WorkoutProgressNotifier', 'Getting today workout session', 
        state: 'userId: $userId, dayId: $workoutProgramDayId');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        lastRefreshTime: DateTime.now(),
      );

      final result = await _apiService.getTodayWorkoutSession(userId, workoutProgramDayId);

      if (result.success && result.data != null) {
        state = state.copyWith(
          currentSession: result.data,
          isLoading: false,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Today workout session loaded successfully', 
          state: 'sessionID: ${result.data!.workoutSessionID}');
      } else {
        state = state.copyWith(
          error: result.message,
          isLoading: false,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Failed to load today workout session', 
          state: result.message);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressNotifier.getTodayWorkoutSession');
      
      state = state.copyWith(
        error: 'Bugünkü antrenman oturumu getirilirken hata oluştu: $e',
        isLoading: false,
      );
    }
  }

  /// Antrenman geçmişini getir
  Future<void> getWorkoutHistory(int userId, {int? limit}) async {
    try {
      LoggingService.stateLog('WorkoutProgressNotifier', 'Getting workout history', 
        state: 'userId: $userId, limit: $limit');

      final result = await _apiService.getWorkoutHistory(userId, limit: limit);

      if (result.success && result.data != null) {
        state = state.copyWith(
          workoutHistory: result.data!,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Workout history loaded successfully', 
          state: 'count: ${result.data!.length}');
      } else {
        LoggingService.stateLog('WorkoutProgressNotifier', 'Failed to load workout history', 
          state: result.message);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressNotifier.getWorkoutHistory');
    }
  }

  /// Egzersiz istatistiklerini getir
  Future<void> getExerciseStats(int userId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      LoggingService.stateLog('WorkoutProgressNotifier', 'Getting exercise stats', 
        state: 'userId: $userId, startDate: $startDate, endDate: $endDate');

      final result = await _apiService.getExerciseStats(userId, startDate: startDate, endDate: endDate);

      if (result.success && result.data != null) {
        state = state.copyWith(
          exerciseStats: result.data!,
        );

        LoggingService.stateLog('WorkoutProgressNotifier', 'Exercise stats loaded successfully', 
          state: 'count: ${result.data!.length}');
      } else {
        LoggingService.stateLog('WorkoutProgressNotifier', 'Failed to load exercise stats', 
          state: result.message);
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressNotifier.getExerciseStats');
    }
  }

  /// Session'ı egzersiz tamamlama ile güncelle
  MobileWorkoutSessionDto _updateSessionWithExerciseCompletion(
    MobileWorkoutSessionDto currentSession,
    MobileExerciseCompletionDto updatedExercise,
    WorkoutSessionStatsDto sessionStats,
  ) {
    final updatedExercises = currentSession.exerciseCompletions.map((exercise) {
      if (exercise.workoutProgramExerciseID == updatedExercise.workoutProgramExerciseID) {
        return updatedExercise;
      }
      return exercise;
    }).toList();

    return MobileWorkoutSessionDto(
      workoutSessionID: currentSession.workoutSessionID,
      workoutProgramDayID: currentSession.workoutProgramDayID,
      dayName: currentSession.dayName,
      sessionDate: currentSession.sessionDate,
      startTime: currentSession.startTime,
      endTime: sessionStats.endTime,
      isCompleted: sessionStats.isCompleted,
      completionPercentage: sessionStats.completionPercentage,
      totalExercises: sessionStats.totalExercises,
      completedExercises: sessionStats.completedExercises,
      exerciseCompletions: updatedExercises,
    );
  }

  /// Hata mesajını temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Mevcut oturumu temizle
  void clearCurrentSession() {
    state = state.copyWith(clearCurrentSession: true);
  }

  /// State'i sıfırla
  void reset() {
    state = const WorkoutProgressState();
  }
}

/// Providers
final workoutProgressApiServiceProvider = Provider<WorkoutProgressApiService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return WorkoutProgressApiServiceImpl(apiService);
});

final workoutProgressProvider = StateNotifierProvider<WorkoutProgressNotifier, WorkoutProgressState>((ref) {
  final apiService = ref.read(workoutProgressApiServiceProvider);
  return WorkoutProgressNotifier(apiService, ref);
});

/// State Getters
final hasActiveWorkoutSessionProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgressProvider).hasActiveSession;
});

final workoutProgressLoadingProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgressProvider).isLoading;
});

final workoutProgressTogglingProvider = Provider<bool>((ref) {
  return ref.watch(workoutProgressProvider).isToggling;
});
