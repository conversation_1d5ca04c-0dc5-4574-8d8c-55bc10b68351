/// Workout Progress API Service - GymKod Pro Mobile
/// 
/// Bu service antrenman ilerleme takibi için backend API çağrılarını yönetir.
library;

import '../../../../core/services/api_service.dart';
import '../../../../core/services/logging_service.dart';
import '../../../../core/models/api_response.dart';
import '../models/workout_progress_models.dart';

/// Workout Progress API Service Interface
abstract class WorkoutProgressApiService {
  /// Antrenman oturumu başlatır veya mevcut oturumu getirir
  Future<ApiResponse<MobileWorkoutSessionDto>> startWorkoutSession(StartWorkoutSessionDto startDto);

  /// Egzersiz tamamlama durumunu toggle eder
  Future<ApiResponse<ExerciseCompletionResponseDto>> toggleExerciseCompletion(ToggleExerciseCompletionDto toggleDto);

  /// Bugünkü aktif antrenman oturumunu getirir
  Future<ApiResponse<MobileWorkoutSessionDto>> getTodayWorkoutSession(int userId, int workoutProgramDayId);

  /// Antrenman oturumu detaylarını getirir
  Future<ApiResponse<MobileWorkoutSessionDto>> getWorkoutSessionDetail(int workoutSessionId);

  /// Antrenman geçmişini getirir
  Future<ApiResponse<List<WorkoutSessionStatsDto>>> getWorkoutHistory(int userId, {int? limit});

  /// Egzersiz istatistiklerini getirir
  Future<ApiResponse<List<ExerciseStatsDto>>> getExerciseStats(int userId, {DateTime? startDate, DateTime? endDate});
}

/// Workout Progress API Service Implementation
class WorkoutProgressApiServiceImpl implements WorkoutProgressApiService {
  final ApiService _apiService;

  WorkoutProgressApiServiceImpl(this._apiService);

  @override
  Future<ApiResponse<MobileWorkoutSessionDto>> startWorkoutSession(StartWorkoutSessionDto startDto) async {
    try {
      LoggingService.apiLog('POST /api/workoutprogress/start-session', 
        'memberID: ${startDto.memberID}, dayID: ${startDto.workoutProgramDayID}');

      final response = await _apiService.post<Map<String, dynamic>>(
        '/workoutprogress/start-session',
        data: startDto.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final apiResponse = ApiResponse<dynamic>.fromJson(response.data!, (json) => json);
        
        if (apiResponse.success && apiResponse.data != null) {
          final workoutSession = MobileWorkoutSessionDto.fromJson(apiResponse.data['workoutSession']);
          return ApiResponse.success(data: workoutSession, message: apiResponse.message);
        } else {
          return ApiResponse.error(message: apiResponse.message ?? 'Antrenman oturumu başlatılamadı');
        }
      } else {
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressApiService.startWorkoutSession');
      return ApiResponse.error(message: 'Antrenman oturumu başlatılırken hata oluştu: $e');
    }
  }

  @override
  Future<ApiResponse<ExerciseCompletionResponseDto>> toggleExerciseCompletion(ToggleExerciseCompletionDto toggleDto) async {
    try {
      LoggingService.apiLog('POST /api/workoutprogress/toggle-exercise', 
        'sessionID: ${toggleDto.workoutSessionID}, exerciseID: ${toggleDto.workoutProgramExerciseID}, completed: ${toggleDto.isCompleted}');

      final response = await _apiService.post<Map<String, dynamic>>(
        '/workoutprogress/toggle-exercise',
        data: toggleDto.toJson(),
      );

      if (response.statusCode == 200 && response.data != null) {
        final apiResponse = ApiResponse<dynamic>.fromJson(response.data!, (json) => json);
        
        if (apiResponse.success && apiResponse.data != null) {
          final completionResponse = ExerciseCompletionResponseDto.fromJson(apiResponse.data);
          return ApiResponse.success(data: completionResponse, message: apiResponse.message);
        } else {
          return ApiResponse.error(message: apiResponse.message ?? 'Egzersiz tamamlama durumu güncellenemedi');
        }
      } else {
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressApiService.toggleExerciseCompletion');
      return ApiResponse.error(message: 'Egzersiz tamamlama durumu güncellenirken hata oluştu: $e');
    }
  }

  @override
  Future<ApiResponse<MobileWorkoutSessionDto>> getTodayWorkoutSession(int userId, int workoutProgramDayId) async {
    try {
      LoggingService.apiLog('GET /api/workoutprogress/today-session', 
        'userId: $userId, dayId: $workoutProgramDayId');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/workoutprogress/today-session',
        queryParameters: {
          'userId': userId,
          'workoutProgramDayId': workoutProgramDayId,
        },
      );

      if (response.statusCode == 200 && response.data != null) {
        final apiResponse = ApiResponse<dynamic>.fromJson(response.data!, (json) => json);
        
        if (apiResponse.success && apiResponse.data != null) {
          final workoutSession = MobileWorkoutSessionDto.fromJson(apiResponse.data);
          return ApiResponse.success(data: workoutSession, message: apiResponse.message);
        } else {
          return ApiResponse.error(message: apiResponse.message ?? 'Bugünkü antrenman oturumu bulunamadı');
        }
      } else {
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressApiService.getTodayWorkoutSession');
      return ApiResponse.error(message: 'Bugünkü antrenman oturumu getirilirken hata oluştu: $e');
    }
  }

  @override
  Future<ApiResponse<MobileWorkoutSessionDto>> getWorkoutSessionDetail(int workoutSessionId) async {
    try {
      LoggingService.apiLog('GET /api/workoutprogress/session-detail', 
        'sessionId: $workoutSessionId');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/workoutprogress/session-detail/$workoutSessionId',
      );

      if (response.statusCode == 200 && response.data != null) {
        final apiResponse = ApiResponse<dynamic>.fromJson(response.data!, (json) => json);
        
        if (apiResponse.success && apiResponse.data != null) {
          final workoutSession = MobileWorkoutSessionDto.fromJson(apiResponse.data);
          return ApiResponse.success(data: workoutSession, message: apiResponse.message);
        } else {
          return ApiResponse.error(message: apiResponse.message ?? 'Antrenman oturumu detayları bulunamadı');
        }
      } else {
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressApiService.getWorkoutSessionDetail');
      return ApiResponse.error(message: 'Antrenman oturumu detayları getirilirken hata oluştu: $e');
    }
  }

  @override
  Future<ApiResponse<List<WorkoutSessionStatsDto>>> getWorkoutHistory(int userId, {int? limit}) async {
    try {
      LoggingService.apiLog('GET /api/workoutprogress/workout-history', 
        'userId: $userId, limit: $limit');

      final queryParams = <String, dynamic>{'userId': userId};
      if (limit != null) queryParams['limit'] = limit;

      final response = await _apiService.get<Map<String, dynamic>>(
        '/workoutprogress/workout-history',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data != null) {
        final apiResponse = ApiResponse<dynamic>.fromJson(response.data!, (json) => json);
        
        if (apiResponse.success && apiResponse.data != null) {
          final List<dynamic> dataList = apiResponse.data as List<dynamic>;
          final workoutHistory = dataList.map((json) => WorkoutSessionStatsDto.fromJson(json)).toList();
          return ApiResponse.success(data: workoutHistory, message: apiResponse.message);
        } else {
          return ApiResponse.error(message: apiResponse.message ?? 'Antrenman geçmişi bulunamadı');
        }
      } else {
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressApiService.getWorkoutHistory');
      return ApiResponse.error(message: 'Antrenman geçmişi getirilirken hata oluştu: $e');
    }
  }

  @override
  Future<ApiResponse<List<ExerciseStatsDto>>> getExerciseStats(int userId, {DateTime? startDate, DateTime? endDate}) async {
    try {
      LoggingService.apiLog('GET /api/workoutprogress/exercise-stats', 
        'userId: $userId, startDate: $startDate, endDate: $endDate');

      final queryParams = <String, dynamic>{'userId': userId};
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final response = await _apiService.get<Map<String, dynamic>>(
        '/workoutprogress/exercise-stats',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200 && response.data != null) {
        final apiResponse = ApiResponse<dynamic>.fromJson(response.data!, (json) => json);
        
        if (apiResponse.success && apiResponse.data != null) {
          final List<dynamic> dataList = apiResponse.data as List<dynamic>;
          final exerciseStats = dataList.map((json) => ExerciseStatsDto.fromJson(json)).toList();
          return ApiResponse.success(data: exerciseStats, message: apiResponse.message);
        } else {
          return ApiResponse.error(message: apiResponse.message ?? 'Egzersiz istatistikleri bulunamadı');
        }
      } else {
        return ApiResponse.error(message: 'Sunucudan geçersiz yanıt alındı');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgressApiService.getExerciseStats');
      return ApiResponse.error(message: 'Egzersiz istatistikleri getirilirken hata oluştu: $e');
    }
  }
}
