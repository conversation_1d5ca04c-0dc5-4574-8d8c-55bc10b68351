/// Workout Progress Controller - GymKod Pro Backend
/// 
/// Bu controller antrenman ilerleme sistemi için API endpoint'lerini sağlar.
using Business.Abstract;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Workout Progress Controller
    /// Antrenman ilerleme takibi için API endpoint'leri
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WorkoutProgressController : ControllerBase
    {
        private readonly IWorkoutProgressService _workoutProgressService;

        public WorkoutProgressController(IWorkoutProgressService workoutProgressService)
        {
            _workoutProgressService = workoutProgressService;
        }

        /// <summary>
        /// Antrenman oturumu başlatır veya mevcut oturumu getirir
        /// </summary>
        /// <param name="startDto">An<PERSON><PERSON><PERSON> başlatma bilgileri</param>
        /// <returns>Antrenman oturumu detayları</returns>
        [HttpPost("start-session")]
        public IActionResult StartWorkoutSession([FromBody] StartWorkoutSessionDto startDto)
        {
            var result = _workoutProgressService.StartWorkoutSession(startDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Egzersiz tamamlama durumunu toggle eder (mobil için)
        /// </summary>
        /// <param name="toggleDto">Egzersiz tamamlama bilgileri</param>
        /// <returns>Güncellenmiş egzersiz durumu</returns>
        [HttpPost("toggle-exercise")]
        public IActionResult ToggleExerciseCompletion([FromBody] ToggleExerciseCompletionDto toggleDto)
        {
            var result = _workoutProgressService.ToggleExerciseCompletion(toggleDto);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kullanıcının bugünkü aktif antrenman oturumunu getirir (mobil için)
        /// </summary>
        /// <param name="userId">Kullanıcı ID</param>
        /// <param name="workoutProgramDayId">Antrenman programı gün ID</param>
        /// <returns>Bugünkü antrenman oturumu</returns>
        [HttpGet("today-session")]
        public IActionResult GetTodayWorkoutSession(int userId, int workoutProgramDayId)
        {
            var result = _workoutProgressService.GetTodayWorkoutSession(userId, workoutProgramDayId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Antrenman oturumu detaylarını getirir
        /// </summary>
        /// <param name="workoutSessionId">Antrenman oturumu ID</param>
        /// <returns>Antrenman oturumu detayları</returns>
        [HttpGet("session-detail/{workoutSessionId}")]
        public IActionResult GetWorkoutSessionDetail(int workoutSessionId)
        {
            var result = _workoutProgressService.GetWorkoutSessionDetail(workoutSessionId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kullanıcının antrenman geçmişini getirir
        /// </summary>
        /// <param name="userId">Kullanıcı ID</param>
        /// <param name="limit">Maksimum kayıt sayısı</param>
        /// <returns>Antrenman geçmişi</returns>
        [HttpGet("workout-history")]
        public IActionResult GetWorkoutHistory(int userId, int? limit = null)
        {
            var result = _workoutProgressService.GetWorkoutHistory(userId, limit);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kullanıcının egzersiz istatistiklerini getirir
        /// </summary>
        /// <param name="userId">Kullanıcı ID</param>
        /// <param name="startDate">Başlangıç tarihi</param>
        /// <param name="endDate">Bitiş tarihi</param>
        /// <returns>Egzersiz istatistikleri</returns>
        [HttpGet("exercise-stats")]
        public IActionResult GetExerciseStats(int userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = _workoutProgressService.GetExerciseStats(userId, startDate, endDate);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Kullanıcının haftalık antrenman istatistiklerini getirir
        /// </summary>
        /// <param name="userId">Kullanıcı ID</param>
        /// <param name="weekStartDate">Hafta başlangıç tarihi</param>
        /// <returns>Haftalık istatistikler</returns>
        [HttpGet("weekly-stats")]
        public IActionResult GetWeeklyStats(int userId, DateTime? weekStartDate = null)
        {
            var result = _workoutProgressService.GetWeeklyStats(userId, weekStartDate);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Antrenman oturumunu tamamlar
        /// </summary>
        /// <param name="workoutSessionId">Antrenman oturumu ID</param>
        /// <returns>İşlem sonucu</returns>
        [HttpPost("complete-session/{workoutSessionId}")]
        public IActionResult CompleteWorkoutSession(int workoutSessionId)
        {
            var result = _workoutProgressService.CompleteWorkoutSession(workoutSessionId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }

        /// <summary>
        /// Antrenman oturumunu iptal eder
        /// </summary>
        /// <param name="workoutSessionId">Antrenman oturumu ID</param>
        /// <returns>İşlem sonucu</returns>
        [HttpPost("cancel-session/{workoutSessionId}")]
        public IActionResult CancelWorkoutSession(int workoutSessionId)
        {
            var result = _workoutProgressService.CancelWorkoutSession(workoutSessionId);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
