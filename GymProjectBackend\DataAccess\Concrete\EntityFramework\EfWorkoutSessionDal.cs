/// Workout Session Entity Framework Data Access - GymKod Pro Backend
/// 
/// Bu class WorkoutSession entity'si için Entity Framework data access implementasyonunu sağlar.
using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// Workout Session Entity Framework Data Access
    /// </summary>
    public class EfWorkoutSessionDal : EfEntityRepositoryBase<WorkoutSession, GymContext>, IWorkoutSessionDal
    {
        public WorkoutSession? GetTodayActiveSession(int memberId, int workoutProgramDayId)
        {
            using var context = new GymContext();
            return context.WorkoutSessions
                .FirstOrDefault(ws => ws.MemberID == memberId 
                    && ws.WorkoutProgramDayID == workoutProgramDayId
                    && ws.SessionDate.Date == DateTime.Today
                    && ws.IsActive);
        }

        public WorkoutSession? GetSessionByDate(int memberId, int workoutProgramDayId, DateTime sessionDate)
        {
            using var context = new GymContext();
            return context.WorkoutSessions
                .FirstOrDefault(ws => ws.MemberID == memberId
                    && ws.WorkoutProgramDayID == workoutProgramDayId
                    && ws.SessionDate.Date == sessionDate.Date
                    && ws.IsActive);
        }

        public List<WorkoutSession> GetMemberWorkoutHistory(int memberId, int? limit = null)
        {
            using var context = new GymContext();
            var query = context.WorkoutSessions
                .Where(ws => ws.MemberID == memberId && ws.IsActive)
                .OrderByDescending(ws => ws.SessionDate)
                .Include(ws => ws.WorkoutProgramTemplate)
                .Include(ws => ws.WorkoutProgramDay);

            if (limit.HasValue)
            {
                query = query.Take(limit.Value);
            }

            return query.ToList();
        }

        public WorkoutSession StartOrGetWorkoutSession(int memberId, int workoutProgramTemplateId, int workoutProgramDayId)
        {
            using var context = new GymContext();
            
            // Bugün için mevcut oturum var mı kontrol et
            var existingSession = context.WorkoutSessions
                .FirstOrDefault(ws => ws.MemberID == memberId 
                    && ws.WorkoutProgramDayID == workoutProgramDayId
                    && ws.SessionDate.Date == DateTime.Today
                    && ws.IsActive);

            if (existingSession != null)
            {
                return existingSession;
            }

            // Günün toplam egzersiz sayısını al
            var totalExercises = context.WorkoutProgramExercises
                .Count(wpe => wpe.WorkoutProgramDayID == workoutProgramDayId && wpe.IsActive);

            // Yeni oturum oluştur
            var newSession = new WorkoutSession
            {
                MemberID = memberId,
                WorkoutProgramTemplateID = workoutProgramTemplateId,
                WorkoutProgramDayID = workoutProgramDayId,
                SessionDate = DateTime.Today,
                StartTime = DateTime.Now,
                TotalExercises = totalExercises,
                IsActive = true,
                CreatedDate = DateTime.Now
            };

            context.WorkoutSessions.Add(newSession);
            context.SaveChanges();

            return newSession;
        }

        public WorkoutSessionStatsDto GetWorkoutSessionStats(int workoutSessionId)
        {
            using var context = new GymContext();
            
            var session = context.WorkoutSessions
                .Include(ws => ws.ExerciseCompletions)
                .FirstOrDefault(ws => ws.WorkoutSessionID == workoutSessionId);

            if (session == null)
                return new WorkoutSessionStatsDto();

            var completedExercises = session.ExerciseCompletions?.Count(ec => ec.IsCompleted) ?? 0;

            return new WorkoutSessionStatsDto
            {
                WorkoutSessionID = session.WorkoutSessionID,
                SessionDate = session.SessionDate,
                TotalExercises = session.TotalExercises,
                CompletedExercises = completedExercises,
                CompletionPercentage = session.CompletionPercentage,
                WorkoutDurationMinutes = session.WorkoutDurationMinutes,
                IsCompleted = session.IsCompleted
            };
        }

        public List<WorkoutSessionStatsDto> GetWeeklyStats(int memberId, DateTime weekStartDate)
        {
            using var context = new GymContext();
            
            var weekEndDate = weekStartDate.AddDays(7);
            
            return context.WorkoutSessions
                .Where(ws => ws.MemberID == memberId 
                    && ws.SessionDate >= weekStartDate 
                    && ws.SessionDate < weekEndDate
                    && ws.IsActive)
                .Select(ws => new WorkoutSessionStatsDto
                {
                    WorkoutSessionID = ws.WorkoutSessionID,
                    SessionDate = ws.SessionDate,
                    TotalExercises = ws.TotalExercises,
                    CompletedExercises = ws.CompletedExercises,
                    CompletionPercentage = ws.CompletionPercentage,
                    WorkoutDurationMinutes = ws.WorkoutDurationMinutes,
                    IsCompleted = ws.IsCompleted
                })
                .OrderBy(ws => ws.SessionDate)
                .ToList();
        }

        public List<WorkoutSessionStatsDto> GetCompanyDailyStats(int companyId, DateTime date)
        {
            using var context = new GymContext();
            
            return context.WorkoutSessions
                .Include(ws => ws.Member)
                .Where(ws => ws.Member!.CompanyID == companyId 
                    && ws.SessionDate.Date == date.Date
                    && ws.IsActive)
                .Select(ws => new WorkoutSessionStatsDto
                {
                    WorkoutSessionID = ws.WorkoutSessionID,
                    SessionDate = ws.SessionDate,
                    TotalExercises = ws.TotalExercises,
                    CompletedExercises = ws.CompletedExercises,
                    CompletionPercentage = ws.CompletionPercentage,
                    WorkoutDurationMinutes = ws.WorkoutDurationMinutes,
                    IsCompleted = ws.IsCompleted
                })
                .ToList();
        }
    }
}
