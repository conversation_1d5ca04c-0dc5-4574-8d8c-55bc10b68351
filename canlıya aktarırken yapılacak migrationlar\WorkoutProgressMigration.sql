-- Workout Progress Migration - GymKod Pro
-- Bu migration antrenman iler<PERSON>e takibi için gerekli tabloları oluşturur
-- <PERSON><PERSON><PERSON><PERSON> tama<PERSON>lama, set tamamlama ve günlük ilerleme takibi

-- 1. <PERSON><PERSON><PERSON><PERSON> (Günlük antrenman kayıtları)
CREATE TABLE WorkoutSessions (
    WorkoutSessionID INT IDENTITY(1,1) PRIMARY KEY,
    MemberID INT NOT NULL,
    WorkoutProgramTemplateID INT NOT NULL,
    WorkoutProgramDayID INT NOT NULL,
    SessionDate DATE NOT NULL,
    StartTime DATETIME2,
    EndTime DATETIME2,
    IsCompleted BIT NOT NULL DEFAULT 0,
    CompletionPercentage DECIMAL(5,2) DEFAULT 0.00, -- 0.00 - 100.00
    TotalExercises INT DEFAULT 0,
    CompletedExercises INT DEFAULT 0,
    Notes NVARCHAR(500),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedDate DATETIME2,
    IsActive BIT NOT NULL DEFAULT 1,
    
    CONSTRAINT FK_WorkoutSessions_Member FOREIGN KEY (MemberID) REFERENCES Members(MemberID),
    CONSTRAINT FK_WorkoutSessions_WorkoutProgramTemplate FOREIGN KEY (WorkoutProgramTemplateID) REFERENCES WorkoutProgramTemplates(WorkoutProgramTemplateID),
    CONSTRAINT FK_WorkoutSessions_WorkoutProgramDay FOREIGN KEY (WorkoutProgramDayID) REFERENCES WorkoutProgramDays(WorkoutProgramDayID)
);

-- 2. Egzersiz Tamamlama Tablosu (Her egzersiz için tamamlama durumu)
CREATE TABLE ExerciseCompletions (
    ExerciseCompletionID INT IDENTITY(1,1) PRIMARY KEY,
    WorkoutSessionID INT NOT NULL,
    WorkoutProgramExerciseID INT NOT NULL,
    IsCompleted BIT NOT NULL DEFAULT 0,
    CompletedSets INT DEFAULT 0,
    TotalSets INT NOT NULL,
    CompletionPercentage DECIMAL(5,2) DEFAULT 0.00, -- 0.00 - 100.00
    ActualReps NVARCHAR(50), -- Gerçekte yapılan tekrar sayısı
    ActualWeight DECIMAL(6,2), -- Kullanılan ağırlık (kg)
    RestTimeUsed INT, -- Kullanılan dinlenme süresi (saniye)
    Notes NVARCHAR(300),
    CompletedAt DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedDate DATETIME2,
    
    CONSTRAINT FK_ExerciseCompletions_WorkoutSession FOREIGN KEY (WorkoutSessionID) REFERENCES WorkoutSessions(WorkoutSessionID),
    CONSTRAINT FK_ExerciseCompletions_WorkoutProgramExercise FOREIGN KEY (WorkoutProgramExerciseID) REFERENCES WorkoutProgramExercises(WorkoutProgramExerciseID)
);

-- 3. Set Tamamlama Tablosu (Her set için detaylı takip)
CREATE TABLE SetCompletions (
    SetCompletionID INT IDENTITY(1,1) PRIMARY KEY,
    ExerciseCompletionID INT NOT NULL,
    SetNumber INT NOT NULL,
    IsCompleted BIT NOT NULL DEFAULT 0,
    ActualReps INT,
    PlannedReps NVARCHAR(20), -- "8-10", "MAX", "12" vb.
    ActualWeight DECIMAL(6,2),
    RestTimeUsed INT, -- Saniye
    Difficulty INT CHECK (Difficulty BETWEEN 1 AND 5), -- 1: Çok Kolay, 5: Çok Zor
    Notes NVARCHAR(200),
    CompletedAt DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT FK_SetCompletions_ExerciseCompletion FOREIGN KEY (ExerciseCompletionID) REFERENCES ExerciseCompletions(ExerciseCompletionID)
);

-- 4. Günlük İstatistikler Tablosu (Hızlı erişim için)
CREATE TABLE DailyWorkoutStats (
    DailyWorkoutStatID INT IDENTITY(1,1) PRIMARY KEY,
    MemberID INT NOT NULL,
    StatDate DATE NOT NULL,
    TotalWorkouts INT DEFAULT 0,
    CompletedWorkouts INT DEFAULT 0,
    TotalExercises INT DEFAULT 0,
    CompletedExercises INT DEFAULT 0,
    TotalSets INT DEFAULT 0,
    CompletedSets INT DEFAULT 0,
    TotalWorkoutTime INT DEFAULT 0, -- Dakika
    AverageCompletionPercentage DECIMAL(5,2) DEFAULT 0.00,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedDate DATETIME2,
    
    CONSTRAINT FK_DailyWorkoutStats_Member FOREIGN KEY (MemberID) REFERENCES Members(MemberID),
    CONSTRAINT UQ_DailyWorkoutStats_Member_Date UNIQUE (MemberID, StatDate)
);

-- 5. Haftalık İstatistikler Tablosu
CREATE TABLE WeeklyWorkoutStats (
    WeeklyWorkoutStatID INT IDENTITY(1,1) PRIMARY KEY,
    MemberID INT NOT NULL,
    WeekStartDate DATE NOT NULL, -- Haftanın başlangıç tarihi (Pazartesi)
    WeekEndDate DATE NOT NULL,
    TotalWorkouts INT DEFAULT 0,
    CompletedWorkouts INT DEFAULT 0,
    WorkoutStreak INT DEFAULT 0, -- Ardışık antrenman günleri
    TotalExercises INT DEFAULT 0,
    CompletedExercises INT DEFAULT 0,
    AverageCompletionPercentage DECIMAL(5,2) DEFAULT 0.00,
    TotalWorkoutTime INT DEFAULT 0, -- Dakika
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    UpdatedDate DATETIME2,
    
    CONSTRAINT FK_WeeklyWorkoutStats_Member FOREIGN KEY (MemberID) REFERENCES Members(MemberID),
    CONSTRAINT UQ_WeeklyWorkoutStats_Member_Week UNIQUE (MemberID, WeekStartDate)
);

-- INDEXLER (Performance için)
-- WorkoutSessions indexleri
CREATE INDEX IX_WorkoutSessions_Member_Date ON WorkoutSessions(MemberID, SessionDate DESC);
CREATE INDEX IX_WorkoutSessions_Member_Active ON WorkoutSessions(MemberID, IsActive, SessionDate DESC);
CREATE INDEX IX_WorkoutSessions_Template_Day ON WorkoutSessions(WorkoutProgramTemplateID, WorkoutProgramDayID);

-- ExerciseCompletions indexleri
CREATE INDEX IX_ExerciseCompletions_Session ON ExerciseCompletions(WorkoutSessionID);
CREATE INDEX IX_ExerciseCompletions_Exercise ON ExerciseCompletions(WorkoutProgramExerciseID);
CREATE INDEX IX_ExerciseCompletions_Completed ON ExerciseCompletions(IsCompleted, CompletedAt DESC);

-- SetCompletions indexleri
CREATE INDEX IX_SetCompletions_Exercise ON SetCompletions(ExerciseCompletionID);
CREATE INDEX IX_SetCompletions_Completed ON SetCompletions(IsCompleted, CompletedAt DESC);

-- DailyWorkoutStats indexleri
CREATE INDEX IX_DailyWorkoutStats_Member_Date ON DailyWorkoutStats(MemberID, StatDate DESC);

-- WeeklyWorkoutStats indexleri
CREATE INDEX IX_WeeklyWorkoutStats_Member_Week ON WeeklyWorkoutStats(MemberID, WeekStartDate DESC);

-- STORED PROCEDURES (İlerleme takibi için)

-- Antrenman oturumu başlatma
CREATE PROCEDURE sp_StartWorkoutSession
    @MemberID INT,
    @WorkoutProgramTemplateID INT,
    @WorkoutProgramDayID INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @SessionID INT;
    DECLARE @TotalExercises INT;

    -- Günün toplam egzersiz sayısını al
    SELECT @TotalExercises = COUNT(*)
    FROM WorkoutProgramExercises wpe
    WHERE wpe.WorkoutProgramDayID = @WorkoutProgramDayID
    AND wpe.IsActive = 1;

    -- Bugün için aktif oturum var mı kontrol et
    IF NOT EXISTS (
        SELECT 1 FROM WorkoutSessions
        WHERE MemberID = @MemberID
        AND WorkoutProgramDayID = @WorkoutProgramDayID
        AND SessionDate = CAST(GETDATE() AS DATE)
        AND IsActive = 1
    )
    BEGIN
        -- Yeni oturum oluştur
        INSERT INTO WorkoutSessions (
            MemberID, WorkoutProgramTemplateID, WorkoutProgramDayID,
            SessionDate, StartTime, TotalExercises, IsActive
        )
        VALUES (
            @MemberID, @WorkoutProgramTemplateID, @WorkoutProgramDayID,
            CAST(GETDATE() AS DATE), GETDATE(), @TotalExercises, 1
        );

        SET @SessionID = SCOPE_IDENTITY();
    END
    ELSE
    BEGIN
        -- Mevcut oturumu al
        SELECT @SessionID = WorkoutSessionID
        FROM WorkoutSessions
        WHERE MemberID = @MemberID
        AND WorkoutProgramDayID = @WorkoutProgramDayID
        AND SessionDate = CAST(GETDATE() AS DATE)
        AND IsActive = 1;
    END

    SELECT @SessionID AS WorkoutSessionID;
END;

-- Egzersiz tamamlama
CREATE PROCEDURE sp_ToggleExerciseCompletion
    @WorkoutSessionID INT,
    @WorkoutProgramExerciseID INT,
    @IsCompleted BIT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ExerciseCompletionID INT;
    DECLARE @TotalSets INT;

    -- Egzersizin toplam set sayısını al
    SELECT @TotalSets = Sets
    FROM WorkoutProgramExercises
    WHERE WorkoutProgramExerciseID = @WorkoutProgramExerciseID;

    -- Mevcut completion kaydı var mı kontrol et
    SELECT @ExerciseCompletionID = ExerciseCompletionID
    FROM ExerciseCompletions
    WHERE WorkoutSessionID = @WorkoutSessionID
    AND WorkoutProgramExerciseID = @WorkoutProgramExerciseID;

    IF @ExerciseCompletionID IS NULL
    BEGIN
        -- Yeni kayıt oluştur
        INSERT INTO ExerciseCompletions (
            WorkoutSessionID, WorkoutProgramExerciseID, IsCompleted,
            CompletedSets, TotalSets, CompletionPercentage, CompletedAt
        )
        VALUES (
            @WorkoutSessionID, @WorkoutProgramExerciseID, @IsCompleted,
            CASE WHEN @IsCompleted = 1 THEN @TotalSets ELSE 0 END,
            @TotalSets,
            CASE WHEN @IsCompleted = 1 THEN 100.00 ELSE 0.00 END,
            CASE WHEN @IsCompleted = 1 THEN GETDATE() ELSE NULL END
        );
    END
    ELSE
    BEGIN
        -- Mevcut kaydı güncelle
        UPDATE ExerciseCompletions
        SET IsCompleted = @IsCompleted,
            CompletedSets = CASE WHEN @IsCompleted = 1 THEN @TotalSets ELSE 0 END,
            CompletionPercentage = CASE WHEN @IsCompleted = 1 THEN 100.00 ELSE 0.00 END,
            CompletedAt = CASE WHEN @IsCompleted = 1 THEN GETDATE() ELSE NULL END,
            UpdatedDate = GETDATE()
        WHERE ExerciseCompletionID = @ExerciseCompletionID;
    END

    -- Workout session'ı güncelle
    EXEC sp_UpdateWorkoutSessionProgress @WorkoutSessionID;

    SELECT 1 AS Success;
END;

-- Workout session progress güncelleme
CREATE PROCEDURE sp_UpdateWorkoutSessionProgress
    @WorkoutSessionID INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @TotalExercises INT;
    DECLARE @CompletedExercises INT;
    DECLARE @CompletionPercentage DECIMAL(5,2);

    -- İstatistikleri hesapla
    SELECT
        @TotalExercises = ws.TotalExercises,
        @CompletedExercises = ISNULL(COUNT(ec.ExerciseCompletionID), 0)
    FROM WorkoutSessions ws
    LEFT JOIN ExerciseCompletions ec ON ws.WorkoutSessionID = ec.WorkoutSessionID
        AND ec.IsCompleted = 1
    WHERE ws.WorkoutSessionID = @WorkoutSessionID
    GROUP BY ws.TotalExercises;

    -- Yüzde hesapla
    SET @CompletionPercentage = CASE
        WHEN @TotalExercises > 0 THEN ROUND((@CompletedExercises * 100.0) / @TotalExercises, 2)
        ELSE 0.00
    END;

    -- Session'ı güncelle
    UPDATE WorkoutSessions
    SET CompletedExercises = @CompletedExercises,
        CompletionPercentage = @CompletionPercentage,
        IsCompleted = CASE WHEN @CompletedExercises >= @TotalExercises THEN 1 ELSE 0 END,
        EndTime = CASE WHEN @CompletedExercises >= @TotalExercises THEN GETDATE() ELSE EndTime END,
        UpdatedDate = GETDATE()
    WHERE WorkoutSessionID = @WorkoutSessionID;
END;

PRINT 'Workout Progress Migration completed successfully!';
