using Core.Entities;
using System;

namespace Entities.DTOs
{
    public class WorkoutProgramExerciseDto : IDto
    {
        public int WorkoutProgramExerciseID { get; set; }
        public int WorkoutProgramDayID { get; set; }
        public string ExerciseType { get; set; } // "System" veya "Company"
        public int ExerciseID { get; set; }
        public string ExerciseName { get; set; } // Join'den gelecek
        public string? ExerciseDescription { get; set; } // Join'den gelecek
        public string? CategoryName { get; set; } // Join'den gelecek
        public int OrderIndex { get; set; }
        public int Sets { get; set; }
        public string Reps { get; set; }
        public int? RestTime { get; set; }
        public string? Notes { get; set; }
        public DateTime? CreationDate { get; set; }
    }

    public class WorkoutProgramExerciseAddDto : IDto
    {
        public string ExerciseType { get; set; } // "System" veya "Company"
        public int ExerciseID { get; set; }
        public int OrderIndex { get; set; }
        public int Sets { get; set; }
        public string Reps { get; set; }
        public int? RestTime { get; set; }
        public string? Notes { get; set; }
    }

    public class WorkoutProgramExerciseUpdateDto : IDto
    {
        public int? WorkoutProgramExerciseID { get; set; } // Null ise yeni egzersiz
        public string ExerciseType { get; set; }
        public int ExerciseID { get; set; }
        public int OrderIndex { get; set; }
        public int Sets { get; set; }
        public string Reps { get; set; }
        public int? RestTime { get; set; }
        public string? Notes { get; set; }
    }
}
