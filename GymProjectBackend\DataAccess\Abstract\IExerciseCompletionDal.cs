/// Exercise Completion Data Access Interface - GymKod Pro Backend
/// 
/// Bu interface ExerciseCompletion entity'si için data access operasyonlarını tanımlar.
using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;

namespace DataAccess.Abstract
{
    /// <summary>
    /// Exercise Completion Data Access Interface
    /// </summary>
    public interface IExerciseCompletionDal : IEntityRepository<ExerciseCompletion>
    {
        /// <summary>
        /// Belirli antrenman oturumundaki egzersiz tamamlamalarını getirir
        /// </summary>
        List<ExerciseCompletion> GetByWorkoutSession(int workoutSessionId);

        /// <summary>
        /// Belirli egzersiz için tamamlama kaydını getirir
        /// </summary>
        ExerciseCompletion? GetByWorkoutSessionAndExercise(int workoutSessionId, int workoutProgramExerciseId);

        /// <summary>
        /// Egzersiz tamamlama durumunu toggle eder
        /// </summary>
        bool ToggleExerciseCompletion(int workoutSessionId, int workoutProgramExerciseId, bool isCompleted);

        /// <summary>
        /// Üyenin egzersiz tamamlama geçmişini getirir
        /// </summary>
        List<ExerciseCompletionHistoryDto> GetMemberExerciseHistory(int memberId, int? exerciseId = null, int? limit = null);

        /// <summary>
        /// Egzersiz bazlı istatistikleri getirir
        /// </summary>
        List<ExerciseStatsDto> GetExerciseStats(int memberId, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// En çok yapılan egzersizleri getirir
        /// </summary>
        List<ExerciseStatsDto> GetMostCompletedExercises(int memberId, int limit = 10);
    }
}
