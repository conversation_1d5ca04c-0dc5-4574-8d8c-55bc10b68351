 using Core.Entities;
using System;

namespace Entities.DTOs
{
    public class MemberWorkoutProgramDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string? MemberPhone { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public int CompanyID { get; set; }
        public DateTime AssignedDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public int DayCount { get; set; } // Program gün sayısı
        public int ExerciseCount { get; set; } // Program egzersiz sayısı
    }

    public class MemberWorkoutProgramAddDto : IDto
    {
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
    }

    public class MemberWorkoutProgramUpdateDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
    }

    public class MemberWorkoutProgramListDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string ProgramName { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime AssignedDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
    }

    // Mobil API için özel DTO
    public class MemberActiveWorkoutProgramDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public int DayCount { get; set; }
        public int ExerciseCount { get; set; }
        // Program detayları (günler ve egzersizler) ayrı endpoint'ten gelecek
    }

    // Mobil API için program detay DTO
    public class WorkoutProgramDetailDto : IDto
    {
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public List<WorkoutProgramDayDetailDto> Days { get; set; } = new List<WorkoutProgramDayDetailDto>();
    }

    // Program günü detay DTO
    public class WorkoutProgramDayDetailDto : IDto
    {
        public int WorkoutProgramDayID { get; set; }
        public int DayNumber { get; set; }
        public string DayName { get; set; }
        public bool IsRestDay { get; set; }
        public List<WorkoutProgramExerciseDetailDto> Exercises { get; set; } = new List<WorkoutProgramExerciseDetailDto>();
    }

    // Program egzersiz detay DTO
    public class WorkoutProgramExerciseDetailDto : IDto
    {
        public int WorkoutProgramExerciseID { get; set; }
        public int OrderIndex { get; set; }
        public string ExerciseName { get; set; }
        public string? ExerciseDescription { get; set; }
        public string? CategoryName { get; set; }
        public int Sets { get; set; }
        public string Reps { get; set; }
        public int? RestTime { get; set; }
        public string? Notes { get; set; }
        public string? Equipment { get; set; }
        public string? MuscleGroups { get; set; }

        // Tamamlama durumu bilgileri (mobil için)
        public bool IsCompleted { get; set; } = false;
        public int CompletedSets { get; set; } = 0;
        public decimal CompletionPercentage { get; set; } = 0.00m;
        public DateTime? CompletedAt { get; set; }
    }

    // Workout Session DTO'ları
    public class WorkoutSessionDto : IDto
    {
        public int WorkoutSessionID { get; set; }
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int WorkoutProgramDayID { get; set; }
        public DateTime SessionDate { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsCompleted { get; set; }
        public decimal CompletionPercentage { get; set; }
        public int TotalExercises { get; set; }
        public int CompletedExercises { get; set; }
        public string? Notes { get; set; }
        public int? WorkoutDurationMinutes { get; set; }
        public bool IsInProgress { get; set; }
        public bool IsToday { get; set; }
    }

    public class ExerciseCompletionDto : IDto
    {
        public int ExerciseCompletionID { get; set; }
        public int WorkoutSessionID { get; set; }
        public int WorkoutProgramExerciseID { get; set; }
        public bool IsCompleted { get; set; }
        public int CompletedSets { get; set; }
        public int TotalSets { get; set; }
        public decimal CompletionPercentage { get; set; }
        public string? ActualReps { get; set; }
        public decimal? ActualWeight { get; set; }
        public int? RestTimeUsed { get; set; }
        public string? Notes { get; set; }
        public DateTime? CompletedAt { get; set; }
        public bool IsStarted { get; set; }
        public bool IsInProgress { get; set; }
        public int RemainingSets { get; set; }
    }

    public class SetCompletionDto : IDto
    {
        public int SetCompletionID { get; set; }
        public int ExerciseCompletionID { get; set; }
        public int SetNumber { get; set; }
        public bool IsCompleted { get; set; }
        public int? ActualReps { get; set; }
        public string? PlannedReps { get; set; }
        public decimal? ActualWeight { get; set; }
        public int? RestTimeUsed { get; set; }
        public int? Difficulty { get; set; }
        public string? Notes { get; set; }
        public DateTime? CompletedAt { get; set; }
        public bool IsTargetMet { get; set; }
        public bool IsAboveTarget { get; set; }
        public bool IsBelowTarget { get; set; }
        public string PerformanceStatus { get; set; }
        public string DifficultyDescription { get; set; }
        public string FormattedRestTime { get; set; }
        public string Summary { get; set; }
    }



    // Atama geçmişi için basit DTO
    public class MemberWorkoutProgramHistoryDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public string ProgramName { get; set; }
        public DateTime AssignedDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
        public string? Notes { get; set; }
    }
}
