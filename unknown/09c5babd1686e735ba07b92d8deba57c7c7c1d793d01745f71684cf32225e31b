-- Üye Program Atama Sistemi View Script
-- Bu script performans için optimize edilmiş view oluşturur

USE [GymProject]
GO

-- CACHE OPTİMİZASYONU İÇİN VİEW (Sık kullanılan join'ler için)
CREATE VIEW [dbo].[vw_MemberWorkoutProgramDetails] AS
SELECT 
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    m.Name AS MemberName,
    m.PhoneNumber AS MemberPhone,
    mwp.WorkoutProgramTemplateID,
    wpt.ProgramName,
    wpt.Description AS ProgramDescription,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    mwp.CompanyID,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    mwp.Notes,
    mwp.IsActive,
    mwp.CreationDate,
    -- Program gün say<PERSON>
    (SELECT COUNT(*) FROM WorkoutProgramDays wpd WHERE wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID) AS DayCount,
    -- Program egzersiz sayısı
    (SELECT COUNT(*) FROM WorkoutProgramExercises wpe 
     INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID 
     WHERE wpd.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID) AS ExerciseCount
FROM MemberWorkoutPrograms mwp
INNER JOIN Members m ON mwp.MemberID = m.MemberID
INNER JOIN WorkoutProgramTemplates wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1
GO

-- View için index (performans için)
CREATE UNIQUE CLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_Clustered] 
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberWorkoutProgramID])
GO

PRINT 'Üye Program Atama View oluşturuldu!'
PRINT '- vw_MemberWorkoutProgramDetails (Detaylı view)'
PRINT 'View indexi eklendi.'
GO
