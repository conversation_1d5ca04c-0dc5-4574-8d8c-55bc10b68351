/// Workout Progress Manager - GymKod Pro Backend
/// 
/// Bu class antrenman ilerleme sistemi için business logic implementasyonunu sağlar.
using Business.Abstract;
using Business.BusinessAspects.Autofac;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.CrossCuttingConcerns.Logging.Log4Net.Loggers;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.DTOs;

namespace Business.Concrete
{
    /// <summary>
    /// Workout Progress Manager
    /// </summary>
    public class WorkoutProgressManager : IWorkoutProgressService
    {
        private readonly IWorkoutSessionDal _workoutSessionDal;
        private readonly IExerciseCompletionDal _exerciseCompletionDal;
        private readonly IMemberDal _memberDal;
        private readonly IWorkoutProgramExerciseDal _workoutProgramExerciseDal;

        public WorkoutProgressManager(
            IWorkoutSessionDal workoutSessionDal,
            IExerciseCompletionDal exerciseCompletionDal,
            IMemberDal memberDal,
            IWorkoutProgramExerciseDal workoutProgramExerciseDal)
        {
            _workoutSessionDal = workoutSessionDal;
            _exerciseCompletionDal = exerciseCompletionDal;
            _memberDal = memberDal;
            _workoutProgramExerciseDal = workoutProgramExerciseDal;
        }

        [LogAspect(typeof(FileLogger))]
        [PerformanceAspect(3)]
        public IDataResult<StartWorkoutSessionResponseDto> StartWorkoutSession(StartWorkoutSessionDto startDto)
        {
            try
            {
                // İş kuralları kontrolü
                var ruleResult = BusinessRules.Run(
                    CheckIfMemberExists(startDto.MemberID)
                );

                if (ruleResult != null)
                {
                    return new ErrorDataResult<StartWorkoutSessionResponseDto>(ruleResult.Message);
                }

                // Antrenman oturumu başlat veya mevcut oturumu getir
                var workoutSession = _workoutSessionDal.StartOrGetWorkoutSession(
                    startDto.MemberID,
                    startDto.WorkoutProgramTemplateID,
                    startDto.WorkoutProgramDayID
                );

                // Egzersiz tamamlamalarını getir
                var exerciseCompletions = _exerciseCompletionDal.GetByWorkoutSession(workoutSession.WorkoutSessionID);

                // Günün egzersizlerini getir
                var dayExercises = _workoutProgramExerciseDal.GetList(
                    wpe => wpe.WorkoutProgramDayID == startDto.WorkoutProgramDayID && wpe.IsActive
                ).Data.OrderBy(wpe => wpe.OrderIndex).ToList();

                // Mobile DTO'ya dönüştür
                var mobileWorkoutSession = new MobileWorkoutSessionDto
                {
                    WorkoutSessionID = workoutSession.WorkoutSessionID,
                    WorkoutProgramDayID = workoutSession.WorkoutProgramDayID,
                    SessionDate = workoutSession.SessionDate,
                    StartTime = workoutSession.StartTime,
                    EndTime = workoutSession.EndTime,
                    IsCompleted = workoutSession.IsCompleted,
                    CompletionPercentage = workoutSession.CompletionPercentage,
                    TotalExercises = workoutSession.TotalExercises,
                    CompletedExercises = workoutSession.CompletedExercises,
                    ExerciseCompletions = dayExercises.Select(exercise =>
                    {
                        var completion = exerciseCompletions.FirstOrDefault(ec => ec.WorkoutProgramExerciseID == exercise.WorkoutProgramExerciseID);
                        return new MobileExerciseCompletionDto
                        {
                            WorkoutProgramExerciseID = exercise.WorkoutProgramExerciseID,
                            ExerciseName = exercise.ExerciseName,
                            Sets = exercise.Sets,
                            Reps = exercise.Reps,
                            IsCompleted = completion?.IsCompleted ?? false,
                            CompletedSets = completion?.CompletedSets ?? 0,
                            CompletionPercentage = completion?.CompletionPercentage ?? 0,
                            CompletedAt = completion?.CompletedAt
                        };
                    }).ToList()
                };

                var response = new StartWorkoutSessionResponseDto
                {
                    Success = true,
                    Message = "Antrenman oturumu başarıyla başlatıldı",
                    WorkoutSession = mobileWorkoutSession
                };

                return new SuccessDataResult<StartWorkoutSessionResponseDto>(response, "Antrenman oturumu hazır");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<StartWorkoutSessionResponseDto>($"Antrenman oturumu başlatılırken hata oluştu: {ex.Message}");
            }
        }

        [LogAspect(typeof(FileLogger))]
        [PerformanceAspect(2)]
        public IDataResult<ExerciseCompletionResponseDto> ToggleExerciseCompletion(ToggleExerciseCompletionDto toggleDto)
        {
            try
            {
                // Egzersiz tamamlama durumunu toggle et
                var success = _exerciseCompletionDal.ToggleExerciseCompletion(
                    toggleDto.WorkoutSessionID,
                    toggleDto.WorkoutProgramExerciseID,
                    toggleDto.IsCompleted
                );

                if (!success)
                {
                    return new ErrorDataResult<ExerciseCompletionResponseDto>("Egzersiz tamamlama durumu güncellenemedi");
                }

                // Güncellenmiş egzersiz bilgisini getir
                var exerciseCompletion = _exerciseCompletionDal.GetByWorkoutSessionAndExercise(
                    toggleDto.WorkoutSessionID,
                    toggleDto.WorkoutProgramExerciseID
                );

                // Egzersiz bilgisini getir
                var exercise = _workoutProgramExerciseDal.Get(wpe => wpe.WorkoutProgramExerciseID == toggleDto.WorkoutProgramExerciseID);

                // Workout session istatistiklerini getir
                var sessionStats = _workoutSessionDal.GetWorkoutSessionStats(toggleDto.WorkoutSessionID);

                var mobileExerciseCompletion = new MobileExerciseCompletionDto
                {
                    WorkoutProgramExerciseID = toggleDto.WorkoutProgramExerciseID,
                    ExerciseName = exercise.Data?.ExerciseName ?? "",
                    Sets = exercise.Data?.Sets ?? 0,
                    Reps = exercise.Data?.Reps ?? "",
                    IsCompleted = exerciseCompletion?.IsCompleted ?? false,
                    CompletedSets = exerciseCompletion?.CompletedSets ?? 0,
                    CompletionPercentage = exerciseCompletion?.CompletionPercentage ?? 0,
                    CompletedAt = exerciseCompletion?.CompletedAt
                };

                var response = new ExerciseCompletionResponseDto
                {
                    Success = true,
                    Message = toggleDto.IsCompleted ? "Egzersiz tamamlandı! 🎉" : "Egzersiz tamamlama geri alındı",
                    ExerciseCompletion = mobileExerciseCompletion,
                    WorkoutSessionStats = sessionStats
                };

                return new SuccessDataResult<ExerciseCompletionResponseDto>(response, response.Message);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<ExerciseCompletionResponseDto>($"Egzersiz tamamlama durumu güncellenirken hata oluştu: {ex.Message}");
            }
        }

        [LogAspect(typeof(FileLogger))]
        [PerformanceAspect(3)]
        public IDataResult<MobileWorkoutSessionDto> GetTodayWorkoutSession(int userId, int workoutProgramDayId)
        {
            try
            {
                // User ID'den Member ID'yi al
                var member = _memberDal.Get(m => m.UserID == userId);
                if (member.Data == null)
                {
                    return new ErrorDataResult<MobileWorkoutSessionDto>("Üye bilgisi bulunamadı");
                }

                // Bugünkü aktif oturumu getir
                var workoutSession = _workoutSessionDal.GetTodayActiveSession(member.Data.MemberID, workoutProgramDayId);
                if (workoutSession == null)
                {
                    return new ErrorDataResult<MobileWorkoutSessionDto>("Bugün için aktif antrenman oturumu bulunamadı");
                }

                // Egzersiz tamamlamalarını getir
                var exerciseCompletions = _exerciseCompletionDal.GetByWorkoutSession(workoutSession.WorkoutSessionID);

                // Günün egzersizlerini getir
                var dayExercises = _workoutProgramExerciseDal.GetList(
                    wpe => wpe.WorkoutProgramDayID == workoutProgramDayId && wpe.IsActive
                ).Data.OrderBy(wpe => wpe.OrderIndex).ToList();

                // Mobile DTO'ya dönüştür
                var mobileWorkoutSession = new MobileWorkoutSessionDto
                {
                    WorkoutSessionID = workoutSession.WorkoutSessionID,
                    WorkoutProgramDayID = workoutSession.WorkoutProgramDayID,
                    SessionDate = workoutSession.SessionDate,
                    StartTime = workoutSession.StartTime,
                    EndTime = workoutSession.EndTime,
                    IsCompleted = workoutSession.IsCompleted,
                    CompletionPercentage = workoutSession.CompletionPercentage,
                    TotalExercises = workoutSession.TotalExercises,
                    CompletedExercises = workoutSession.CompletedExercises,
                    ExerciseCompletions = dayExercises.Select(exercise =>
                    {
                        var completion = exerciseCompletions.FirstOrDefault(ec => ec.WorkoutProgramExerciseID == exercise.WorkoutProgramExerciseID);
                        return new MobileExerciseCompletionDto
                        {
                            WorkoutProgramExerciseID = exercise.WorkoutProgramExerciseID,
                            ExerciseName = exercise.ExerciseName,
                            Sets = exercise.Sets,
                            Reps = exercise.Reps,
                            IsCompleted = completion?.IsCompleted ?? false,
                            CompletedSets = completion?.CompletedSets ?? 0,
                            CompletionPercentage = completion?.CompletionPercentage ?? 0,
                            CompletedAt = completion?.CompletedAt
                        };
                    }).ToList()
                };

                return new SuccessDataResult<MobileWorkoutSessionDto>(mobileWorkoutSession, "Bugünkü antrenman oturumu getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MobileWorkoutSessionDto>($"Antrenman oturumu getirilirken hata oluştu: {ex.Message}");
            }
        }

        // Diğer metodlar için placeholder'lar (devam edecek...)
        public IDataResult<MobileWorkoutSessionDto> GetWorkoutSessionDetail(int workoutSessionId)
        {
            throw new NotImplementedException();
        }

        public IDataResult<List<WorkoutSessionStatsDto>> GetWorkoutHistory(int userId, int? limit = null)
        {
            throw new NotImplementedException();
        }

        public IDataResult<List<ExerciseStatsDto>> GetExerciseStats(int userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            throw new NotImplementedException();
        }

        public IDataResult<List<WorkoutSessionStatsDto>> GetWeeklyStats(int userId, DateTime? weekStartDate = null)
        {
            throw new NotImplementedException();
        }

        public IResult CompleteWorkoutSession(int workoutSessionId)
        {
            throw new NotImplementedException();
        }

        public IResult CancelWorkoutSession(int workoutSessionId)
        {
            throw new NotImplementedException();
        }

        // İş kuralları
        private IResult CheckIfMemberExists(int memberId)
        {
            var result = _memberDal.Get(m => m.MemberID == memberId);
            if (result.Data == null)
            {
                return new ErrorResult("Üye bulunamadı");
            }
            return new SuccessResult();
        }
    }
}
