// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workout_progress_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StartWorkoutSessionDto _$StartWorkoutSessionDtoFromJson(
  Map<String, dynamic> json,
) => StartWorkoutSessionDto(
  memberID: (json['memberID'] as num).toInt(),
  workoutProgramTemplateID: (json['workoutProgramTemplateID'] as num).toInt(),
  workoutProgramDayID: (json['workoutProgramDayID'] as num).toInt(),
);

Map<String, dynamic> _$StartWorkoutSessionDtoToJson(
  StartWorkoutSessionDto instance,
) => <String, dynamic>{
  'memberID': instance.memberID,
  'workoutProgramTemplateID': instance.workoutProgramTemplateID,
  'workoutProgramDayID': instance.workoutProgramDayID,
};

ToggleExerciseCompletionDto _$ToggleExerciseCompletionDtoFromJson(
  Map<String, dynamic> json,
) => ToggleExerciseCompletionDto(
  workoutSessionID: (json['workoutSessionID'] as num).toInt(),
  workoutProgramExerciseID: (json['workoutProgramExerciseID'] as num).toInt(),
  isCompleted: json['isCompleted'] as bool,
);

Map<String, dynamic> _$ToggleExerciseCompletionDtoToJson(
  ToggleExerciseCompletionDto instance,
) => <String, dynamic>{
  'workoutSessionID': instance.workoutSessionID,
  'workoutProgramExerciseID': instance.workoutProgramExerciseID,
  'isCompleted': instance.isCompleted,
};

MobileWorkoutSessionDto _$MobileWorkoutSessionDtoFromJson(
  Map<String, dynamic> json,
) => MobileWorkoutSessionDto(
  workoutSessionID: (json['workoutSessionID'] as num).toInt(),
  workoutProgramDayID: (json['workoutProgramDayID'] as num).toInt(),
  dayName: json['dayName'] as String? ?? '',
  sessionDate: DateTime.parse(json['sessionDate'] as String),
  startTime:
      json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
  endTime:
      json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
  isCompleted: json['isCompleted'] as bool? ?? false,
  completionPercentage:
      (json['completionPercentage'] as num?)?.toDouble() ?? 0.0,
  totalExercises: (json['totalExercises'] as num?)?.toInt() ?? 0,
  completedExercises: (json['completedExercises'] as num?)?.toInt() ?? 0,
  exerciseCompletions:
      (json['exerciseCompletions'] as List<dynamic>?)
          ?.map(
            (e) =>
                MobileExerciseCompletionDto.fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
);

Map<String, dynamic> _$MobileWorkoutSessionDtoToJson(
  MobileWorkoutSessionDto instance,
) => <String, dynamic>{
  'workoutSessionID': instance.workoutSessionID,
  'workoutProgramDayID': instance.workoutProgramDayID,
  'dayName': instance.dayName,
  'sessionDate': instance.sessionDate.toIso8601String(),
  'startTime': instance.startTime?.toIso8601String(),
  'endTime': instance.endTime?.toIso8601String(),
  'isCompleted': instance.isCompleted,
  'completionPercentage': instance.completionPercentage,
  'totalExercises': instance.totalExercises,
  'completedExercises': instance.completedExercises,
  'exerciseCompletions': instance.exerciseCompletions,
};

MobileExerciseCompletionDto _$MobileExerciseCompletionDtoFromJson(
  Map<String, dynamic> json,
) => MobileExerciseCompletionDto(
  workoutProgramExerciseID: (json['workoutProgramExerciseID'] as num).toInt(),
  exerciseName: json['exerciseName'] as String,
  sets: (json['sets'] as num).toInt(),
  reps: json['reps'] as String,
  isCompleted: json['isCompleted'] as bool? ?? false,
  completedSets: (json['completedSets'] as num?)?.toInt() ?? 0,
  completionPercentage:
      (json['completionPercentage'] as num?)?.toDouble() ?? 0.0,
  completedAt:
      json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
);

Map<String, dynamic> _$MobileExerciseCompletionDtoToJson(
  MobileExerciseCompletionDto instance,
) => <String, dynamic>{
  'workoutProgramExerciseID': instance.workoutProgramExerciseID,
  'exerciseName': instance.exerciseName,
  'sets': instance.sets,
  'reps': instance.reps,
  'isCompleted': instance.isCompleted,
  'completedSets': instance.completedSets,
  'completionPercentage': instance.completionPercentage,
  'completedAt': instance.completedAt?.toIso8601String(),
};

ExerciseCompletionResponseDto _$ExerciseCompletionResponseDtoFromJson(
  Map<String, dynamic> json,
) => ExerciseCompletionResponseDto(
  success: json['success'] as bool,
  message: json['message'] as String,
  exerciseCompletion:
      json['exerciseCompletion'] == null
          ? null
          : MobileExerciseCompletionDto.fromJson(
            json['exerciseCompletion'] as Map<String, dynamic>,
          ),
  workoutSessionStats:
      json['workoutSessionStats'] == null
          ? null
          : WorkoutSessionStatsDto.fromJson(
            json['workoutSessionStats'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$ExerciseCompletionResponseDtoToJson(
  ExerciseCompletionResponseDto instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'exerciseCompletion': instance.exerciseCompletion,
  'workoutSessionStats': instance.workoutSessionStats,
};

WorkoutSessionStatsDto _$WorkoutSessionStatsDtoFromJson(
  Map<String, dynamic> json,
) => WorkoutSessionStatsDto(
  workoutSessionID: (json['workoutSessionID'] as num).toInt(),
  sessionDate: DateTime.parse(json['sessionDate'] as String),
  totalExercises: (json['totalExercises'] as num?)?.toInt() ?? 0,
  completedExercises: (json['completedExercises'] as num?)?.toInt() ?? 0,
  completionPercentage:
      (json['completionPercentage'] as num?)?.toDouble() ?? 0.0,
  workoutDurationMinutes: (json['workoutDurationMinutes'] as num?)?.toInt(),
  isCompleted: json['isCompleted'] as bool? ?? false,
  startTime:
      json['startTime'] == null
          ? null
          : DateTime.parse(json['startTime'] as String),
  endTime:
      json['endTime'] == null
          ? null
          : DateTime.parse(json['endTime'] as String),
);

Map<String, dynamic> _$WorkoutSessionStatsDtoToJson(
  WorkoutSessionStatsDto instance,
) => <String, dynamic>{
  'workoutSessionID': instance.workoutSessionID,
  'sessionDate': instance.sessionDate.toIso8601String(),
  'totalExercises': instance.totalExercises,
  'completedExercises': instance.completedExercises,
  'completionPercentage': instance.completionPercentage,
  'workoutDurationMinutes': instance.workoutDurationMinutes,
  'isCompleted': instance.isCompleted,
  'startTime': instance.startTime?.toIso8601String(),
  'endTime': instance.endTime?.toIso8601String(),
};

ExerciseStatsDto _$ExerciseStatsDtoFromJson(Map<String, dynamic> json) =>
    ExerciseStatsDto(
      exerciseID: (json['exerciseID'] as num).toInt(),
      exerciseName: json['exerciseName'] as String,
      totalCompletions: (json['totalCompletions'] as num?)?.toInt() ?? 0,
      averageCompletionPercentage:
          (json['averageCompletionPercentage'] as num?)?.toDouble() ?? 0.0,
      lastCompletedAt:
          json['lastCompletedAt'] == null
              ? null
              : DateTime.parse(json['lastCompletedAt'] as String),
    );

Map<String, dynamic> _$ExerciseStatsDtoToJson(ExerciseStatsDto instance) =>
    <String, dynamic>{
      'exerciseID': instance.exerciseID,
      'exerciseName': instance.exerciseName,
      'totalCompletions': instance.totalCompletions,
      'averageCompletionPercentage': instance.averageCompletionPercentage,
      'lastCompletedAt': instance.lastCompletedAt?.toIso8601String(),
    };
