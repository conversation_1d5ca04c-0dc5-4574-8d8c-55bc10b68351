/// Workout Progress Models - GymKod Pro Mobile
/// 
/// Bu dosya antrenman ilerleme takibi için gerek<PERSON> model sınıflarını içerir.
library;

import 'package:json_annotation/json_annotation.dart';

part 'workout_progress_models.g.dart';

/// Antrenman oturumu başlatma DTO'su
@JsonSerializable()
class StartWorkoutSessionDto {
  final int memberID;
  final int workoutProgramTemplateID;
  final int workoutProgramDayID;

  const StartWorkoutSessionDto({
    required this.memberID,
    required this.workoutProgramTemplateID,
    required this.workoutProgramDayID,
  });

  factory StartWorkoutSessionDto.fromJson(Map<String, dynamic> json) =>
      _$StartWorkoutSessionDtoFromJson(json);

  Map<String, dynamic> toJson() => _$StartWorkoutSessionDtoToJson(this);
}

/// Egzersiz tamamlama toggle DTO'su
@JsonSerializable()
class ToggleExerciseCompletionDto {
  final int workoutSessionID;
  final int workoutProgramExerciseID;
  final bool isCompleted;

  const ToggleExerciseCompletionDto({
    required this.workoutSessionID,
    required this.workoutProgramExerciseID,
    required this.isCompleted,
  });

  factory ToggleExerciseCompletionDto.fromJson(Map<String, dynamic> json) =>
      _$ToggleExerciseCompletionDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ToggleExerciseCompletionDtoToJson(this);
}

/// Mobil antrenman oturumu DTO'su
@JsonSerializable()
class MobileWorkoutSessionDto {
  final int workoutSessionID;
  final int workoutProgramDayID;
  final String dayName;
  final DateTime sessionDate;
  final DateTime? startTime;
  final DateTime? endTime;
  final bool isCompleted;
  final double completionPercentage;
  final int totalExercises;
  final int completedExercises;
  final List<MobileExerciseCompletionDto> exerciseCompletions;

  const MobileWorkoutSessionDto({
    required this.workoutSessionID,
    required this.workoutProgramDayID,
    this.dayName = '',
    required this.sessionDate,
    this.startTime,
    this.endTime,
    this.isCompleted = false,
    this.completionPercentage = 0.0,
    this.totalExercises = 0,
    this.completedExercises = 0,
    this.exerciseCompletions = const [],
  });

  factory MobileWorkoutSessionDto.fromJson(Map<String, dynamic> json) =>
      _$MobileWorkoutSessionDtoFromJson(json);

  Map<String, dynamic> toJson() => _$MobileWorkoutSessionDtoToJson(this);

  /// Antrenman devam ediyor mu
  bool get isInProgress {
    return startTime != null && endTime == null && !isCompleted;
  }

  /// Bugünkü antrenman mı
  bool get isToday {
    return sessionDate.day == DateTime.now().day &&
           sessionDate.month == DateTime.now().month &&
           sessionDate.year == DateTime.now().year;
  }

  /// Antrenman süresi (dakika)
  int? get workoutDurationMinutes {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!).inMinutes;
    }
    return null;
  }
}

/// Mobil egzersiz tamamlama DTO'su
@JsonSerializable()
class MobileExerciseCompletionDto {
  final int workoutProgramExerciseID;
  final String exerciseName;
  final int sets;
  final String reps;
  final bool isCompleted;
  final int completedSets;
  final double completionPercentage;
  final DateTime? completedAt;

  const MobileExerciseCompletionDto({
    required this.workoutProgramExerciseID,
    required this.exerciseName,
    required this.sets,
    required this.reps,
    this.isCompleted = false,
    this.completedSets = 0,
    this.completionPercentage = 0.0,
    this.completedAt,
  });

  factory MobileExerciseCompletionDto.fromJson(Map<String, dynamic> json) =>
      _$MobileExerciseCompletionDtoFromJson(json);

  Map<String, dynamic> toJson() => _$MobileExerciseCompletionDtoToJson(this);

  /// Egzersiz durumu ikonu
  String get statusIcon {
    if (isCompleted) return '✅';
    if (completedSets > 0) return '⏳';
    return '⭕';
  }

  /// Egzersiz durumu açıklaması
  String get statusDescription {
    if (isCompleted) return 'Tamamlandı';
    if (completedSets > 0) return 'Devam ediyor ($completedSets/$sets set)';
    return 'Bekliyor';
  }

  /// Kalan set sayısı
  int get remainingSets {
    return sets - completedSets;
  }

  /// Egzersiz başlatıldı mı
  bool get isStarted {
    return completedSets > 0;
  }

  /// Egzersiz devam ediyor mu
  bool get isInProgress {
    return isStarted && !isCompleted;
  }
}

/// Egzersiz tamamlama yanıt DTO'su
@JsonSerializable()
class ExerciseCompletionResponseDto {
  final bool success;
  final String message;
  final MobileExerciseCompletionDto? exerciseCompletion;
  final WorkoutSessionStatsDto? workoutSessionStats;

  const ExerciseCompletionResponseDto({
    required this.success,
    required this.message,
    this.exerciseCompletion,
    this.workoutSessionStats,
  });

  factory ExerciseCompletionResponseDto.fromJson(Map<String, dynamic> json) =>
      _$ExerciseCompletionResponseDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ExerciseCompletionResponseDtoToJson(this);
}

/// Antrenman oturumu istatistikleri DTO'su
@JsonSerializable()
class WorkoutSessionStatsDto {
  final int workoutSessionID;
  final DateTime sessionDate;
  final int totalExercises;
  final int completedExercises;
  final double completionPercentage;
  final int? workoutDurationMinutes;
  final bool isCompleted;
  final DateTime? startTime;
  final DateTime? endTime;

  const WorkoutSessionStatsDto({
    required this.workoutSessionID,
    required this.sessionDate,
    this.totalExercises = 0,
    this.completedExercises = 0,
    this.completionPercentage = 0.0,
    this.workoutDurationMinutes,
    this.isCompleted = false,
    this.startTime,
    this.endTime,
  });

  factory WorkoutSessionStatsDto.fromJson(Map<String, dynamic> json) =>
      _$WorkoutSessionStatsDtoFromJson(json);

  Map<String, dynamic> toJson() => _$WorkoutSessionStatsDtoToJson(this);
}

/// Egzersiz istatistikleri DTO'su
@JsonSerializable()
class ExerciseStatsDto {
  final int exerciseID;
  final String exerciseName;
  final int totalCompletions;
  final double averageCompletionPercentage;
  final DateTime? lastCompletedAt;

  const ExerciseStatsDto({
    required this.exerciseID,
    required this.exerciseName,
    this.totalCompletions = 0,
    this.averageCompletionPercentage = 0.0,
    this.lastCompletedAt,
  });

  factory ExerciseStatsDto.fromJson(Map<String, dynamic> json) =>
      _$ExerciseStatsDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ExerciseStatsDtoToJson(this);
}
