/// Workout Program Page - GymKod Pro Mobile
///
/// Bu sayfa üyelerin antrenman programlarını görüntülemesi için oluşturulmuştur.
/// Backend API'den gerçek antrenman programı verilerini alır ve gösterir.
///
/// RESPONSIVE DESIGN:
/// - Responsive card layout ve grid system
/// - Responsive typography scaling
/// - Responsive spacing ve padding
/// - Responsive icon sizes ve container dimensions
/// - Responsive workout card layout
library;

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../../../workout/presentation/providers/workout_program_provider.dart';
import '../../../workout/presentation/providers/workout_progress_provider.dart';
import '../../../workout/data/models/workout_program_models.dart';
import '../../../workout/data/models/workout_progress_models.dart';
import '../../../workout/presentation/widgets/animated_checkbox.dart';
import '../../../auth/presentation/providers/auth_provider.dart';

/// Workout Program Page
/// Member rolündeki kullanıcılar için antrenman programı sayfası
class WorkoutProgramPage extends ConsumerStatefulWidget {
  const WorkoutProgramPage({super.key});

  @override
  ConsumerState<WorkoutProgramPage> createState() => _WorkoutProgramPageState();
}

class _WorkoutProgramPageState extends ConsumerState<WorkoutProgramPage> {
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında antrenman programlarını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(workoutProgramProvider.notifier).loadActivePrograms();
      LoggingService.info('Workout program page loaded', tag: 'WORKOUT');
    });

    // Yenile butonu için timer başlat
    _startRefreshTimer();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  /// Yenile butonu için timer (10 saniye kuralı)
  void _startRefreshTimer() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {}); // UI'ı güncelle (kalan saniye için)
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final workoutState = ref.watch(workoutProgramProvider);

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Responsive Content Section
                    Expanded(
                      child: Padding(
                        padding: AppSpacing.responsiveScreenPadding(context),
                        child: _buildResponsiveWorkoutContent(theme, deviceType, workoutState),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }



  /// Responsive Workout Content
  Widget _buildResponsiveWorkoutContent(ThemeData theme, DeviceType deviceType, WorkoutProgramState workoutState) {
    return SingleChildScrollView(
      child: Column(
        children: [
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),

          // Loading durumu
          if (workoutState.isLoading)
            _buildLoadingContent(theme, deviceType)
          // Hata durumu
          else if (workoutState.hasError)
            _buildErrorContent(theme, deviceType, workoutState.error!)
          // Program yoksa
          else if (!workoutState.hasPrograms)
            _buildNoProgramContent(theme, deviceType, workoutState)
          // Programlar varsa
          else
            _buildProgramsContent(theme, deviceType, workoutState.programs),
        ],
      ),
    );
  }

  /// Loading Content
  Widget _buildLoadingContent(ThemeData theme, DeviceType deviceType) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Antrenman programlarınız yükleniyor...',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Error Content
  Widget _buildErrorContent(ThemeData theme, DeviceType deviceType, String error) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 40.0,
              tablet: 48.0,
              desktop: 56.0,
            ),
            color: theme.colorScheme.error,
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            'Hata',
            textType: 'h3',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 6.0,
            tablet: 8.0,
            desktop: 10.0,
          ),
          ResponsiveText(
            error,
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(workoutProgramProvider.notifier).loadActivePrograms(forceRefresh: true);
            },
            child: const ResponsiveText(
              'Tekrar Dene',
              textType: 'button',
            ),
          ),
        ],
      ),
    );
  }

  /// No Program Content (Program yoksa)
  Widget _buildNoProgramContent(ThemeData theme, DeviceType deviceType, WorkoutProgramState workoutState) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          Icon(
            Icons.fitness_center_outlined,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 60.0,
              tablet: 72.0,
              desktop: 84.0,
            ),
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),
          ResponsiveText(
            'Henüz antrenman programınız yok',
            textType: 'h3',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          ResponsiveSpacing.vertical(
            mobile: 8.0,
            tablet: 12.0,
            desktop: 16.0,
          ),
          ResponsiveText(
            'Salon yöneticinize danışın',
            textType: 'bodymedium',
            textAlign: TextAlign.center,
          ),
          ResponsiveSpacing.vertical(
            mobile: 20.0,
            tablet: 24.0,
            desktop: 28.0,
          ),
          ElevatedButton.icon(
            onPressed: workoutState.canRefreshNow ? () {
              ref.read(workoutProgramProvider.notifier).loadActivePrograms(forceRefresh: true);
            } : null,
            icon: Icon(
              workoutState.canRefreshNow ? Icons.refresh : Icons.timer,
              size: AppSpacing.responsiveIconSize(context,
                mobile: 18.0,
                tablet: 20.0,
                desktop: 22.0,
              ),
            ),
            label: ResponsiveText(
              workoutState.canRefreshNow
                ? 'Yenile'
                : 'Yenile (${workoutState.remainingSecondsToRefresh}s)',
              textType: 'button',
            ),
          ),
        ],
      ),
    );
  }

  /// Programs Content (Programlar varsa)
  Widget _buildProgramsContent(ThemeData theme, DeviceType deviceType, List<MemberActiveWorkoutProgram> programs) {
    return Column(
      children: [
        // Yenile butonu
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            IconButton(
              onPressed: ref.watch(workoutProgramProvider).canRefreshNow ? () {
                ref.read(workoutProgramProvider.notifier).loadActivePrograms(forceRefresh: true);
              } : null,
              icon: const Icon(Icons.refresh),
              tooltip: 'Yenile',
            ),
          ],
        ),

        ResponsiveSpacing.vertical(
          mobile: 8.0,
          tablet: 12.0,
          desktop: 16.0,
        ),

        // Program listesi
        ...programs.map((program) => Padding(
          padding: EdgeInsets.only(
            bottom: AppSpacing.responsive(context,
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),
          ),
          child: _buildProgramCard(theme, deviceType, program),
        )),
      ],
    );
  }

  /// Program Card
  Widget _buildProgramCard(ThemeData theme, DeviceType deviceType, MemberActiveWorkoutProgram program) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Program başlığı
          Row(
            children: [
              Icon(
                Icons.fitness_center,
                size: AppSpacing.responsiveIconSize(context,
                  mobile: 20.0,
                  tablet: 24.0,
                  desktop: 28.0,
                ),
                color: theme.colorScheme.primary,
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 12.0,
                desktop: 16.0,
              ),
              Expanded(
                child: ResponsiveText(
                  program.programName,
                  textType: 'h4',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          if (program.programDescription != null) ...[
            ResponsiveSpacing.vertical(
              mobile: 8.0,
              tablet: 12.0,
              desktop: 16.0,
            ),
            ResponsiveText(
              program.programDescription!,
              textType: 'bodymedium',
            ),
          ],

          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),

          // Program bilgileri
          Row(
            children: [
              if (program.experienceLevel != null) ...[
                _buildInfoChip(theme, program.experienceLevel!, Icons.trending_up),
                ResponsiveSpacing.horizontal(
                  mobile: 8.0,
                  tablet: 12.0,
                  desktop: 16.0,
                ),
              ],
              if (program.targetGoal != null) ...[
                _buildInfoChip(theme, program.targetGoal!, Icons.flag),
                ResponsiveSpacing.horizontal(
                  mobile: 8.0,
                  tablet: 12.0,
                  desktop: 16.0,
                ),
              ],
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),

          // İstatistikler
          Row(
            children: [
              Expanded(
                child: _buildStatCard(theme, '${program.dayCount}', 'Gün'),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 12.0,
                desktop: 16.0,
              ),
              Expanded(
                child: _buildStatCard(theme, '${program.exerciseCount}', 'Egzersiz'),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 12.0,
                desktop: 16.0,
              ),
              Expanded(
                child: _buildStatCard(theme, '0%', 'Tamamlandı'), // TODO: Gerçek ilerleme
              ),
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),

          // Detay butonu
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                ref.read(workoutProgramProvider.notifier).loadProgramDetail(program.workoutProgramTemplateID);
                _showProgramDetailModal(context, theme);
              },
              child: const ResponsiveText(
                'Detayları Gör',
                textType: 'button',
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Info Chip
  Widget _buildInfoChip(ThemeData theme, String text, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppSpacing.responsive(context,
          mobile: 8.0,
          tablet: 10.0,
          desktop: 12.0,
        ),
        vertical: AppSpacing.responsive(context,
          mobile: 4.0,
          tablet: 6.0,
          desktop: 8.0,
        ),
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 12.0,
          tablet: 14.0,
          desktop: 16.0,
        )),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 14.0,
              tablet: 16.0,
              desktop: 18.0,
            ),
            color: theme.colorScheme.primary,
          ),
          ResponsiveSpacing.horizontal(
            mobile: 4.0,
            tablet: 6.0,
            desktop: 8.0,
          ),
          ResponsiveText(
            text,
            textType: 'labelsmall',
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Stat Card
  Widget _buildStatCard(ThemeData theme, String value, String label) {
    return Container(
      padding: AppSpacing.responsiveCardPadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 8.0,
          tablet: 10.0,
          desktop: 12.0,
        )),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          ResponsiveText(
            value,
            textType: 'h4',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 2.0,
            tablet: 4.0,
            desktop: 6.0,
          ),
          ResponsiveText(
            label,
            textType: 'labelsmall',
            style: TextStyle(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Program Detail Modal
  void _showProgramDetailModal(BuildContext context, ThemeData theme) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: theme.colorScheme.outline.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Content
              Expanded(
                child: Consumer(
                  builder: (context, ref, child) {
                    final workoutState = ref.watch(workoutProgramProvider);

                    if (workoutState.isLoadingDetail) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (workoutState.selectedProgramDetail == null) {
                      return const Center(
                        child: ResponsiveText(
                          'Program detayları yüklenemedi',
                          textType: 'bodymedium',
                        ),
                      );
                    }

                    final program = workoutState.selectedProgramDetail!;

                    return ListView(
                      controller: scrollController,
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Program başlığı
                        ResponsiveText(
                          program.programName,
                          textType: 'h3',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),

                        if (program.programDescription != null) ...[
                          const SizedBox(height: 8),
                          ResponsiveText(
                            program.programDescription!,
                            textType: 'bodymedium',
                          ),
                        ],

                        const SizedBox(height: 20),

                        // Günler
                        ...program.days.map((day) => Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: _buildDayCard(theme, day),
                        )),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Day Card
  Widget _buildDayCard(ThemeData theme, WorkoutProgramDay day) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Gün başlığı
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: day.isRestDay ? theme.colorScheme.secondary : theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ResponsiveText(
                    'Gün ${day.dayNumber}',
                    textType: 'labelmedium',
                    style: TextStyle(
                      color: day.isRestDay ? theme.colorScheme.onSecondary : theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ResponsiveText(
                    day.dayName,
                    textType: 'h5',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                // Gün durumu ikonu
                Text(
                  day.statusIcon,
                  style: const TextStyle(fontSize: 20),
                ),
              ],
            ),

            // Gün tamamlama çubuğu
            if (!day.isRestDay && day.exercises.isNotEmpty) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: day.completionPercentage / 100,
                      backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        day.isCompleted ? Colors.green : theme.colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ResponsiveText(
                    '${day.completionPercentage.toInt()}%',
                    textType: 'labelsmall',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              ResponsiveText(
                day.statusDescription,
                textType: 'labelsmall',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],

            if (day.isRestDay) ...[
              const SizedBox(height: 12),
              ResponsiveText(
                'Dinlenme günü',
                textType: 'bodymedium',
                style: TextStyle(
                  color: theme.colorScheme.secondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ] else ...[
              const SizedBox(height: 16),
              // Egzersizler
              ...day.exercises.map((exercise) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildExerciseCard(theme, exercise),
              )),
            ],
          ],
        ),
      ),
    );
  }

  /// Exercise Card with Checkbox
  Widget _buildExerciseCard(ThemeData theme, WorkoutProgramExercise exercise) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: exercise.isCompleted
          ? theme.colorScheme.primary.withValues(alpha: 0.1)
          : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: exercise.isCompleted
            ? theme.colorScheme.primary
            : theme.colorScheme.outline.withValues(alpha: 0.2),
          width: exercise.isCompleted ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Egzersiz başlığı ve checkbox
          Row(
            children: [
              // Animated Checkbox
              AnimatedCheckbox(
                isChecked: exercise.isCompleted,
                onTap: () => _toggleExerciseCompletion(exercise),
                activeColor: theme.colorScheme.primary,
                size: 24.0,
              ),
              const SizedBox(width: 12),

              // Egzersiz sırası
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: exercise.isCompleted
                    ? theme.colorScheme.primary.withValues(alpha: 0.2)
                    : theme.colorScheme.outline.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: ResponsiveText(
                    '${exercise.orderIndex}',
                    textType: 'labelsmall',
                    style: TextStyle(
                      color: exercise.isCompleted
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Egzersiz adı ve bilgileri
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      exercise.exerciseName,
                      textType: 'bodymedium',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        decoration: exercise.isCompleted
                          ? TextDecoration.lineThrough
                          : TextDecoration.none,
                        color: exercise.isCompleted
                          ? theme.colorScheme.onSurface.withValues(alpha: 0.7)
                          : theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2),
                    ResponsiveText(
                      '${exercise.sets} set × ${exercise.reps} tekrar',
                      textType: 'labelsmall',
                      style: TextStyle(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                        decoration: exercise.isCompleted
                          ? TextDecoration.lineThrough
                          : TextDecoration.none,
                      ),
                    ),
                  ],
                ),
              ),

              // Durum ikonu
              Text(
                exercise.statusIcon,
                style: const TextStyle(fontSize: 18),
              ),
            ],
          ),

          // Set progress (eğer başlatılmışsa)
          if (exercise.isStarted && !exercise.isCompleted) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: exercise.completionPercentage / 100,
                    backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                  ),
                ),
                const SizedBox(width: 8),
                ResponsiveText(
                  '${exercise.completedSets}/${exercise.sets}',
                  textType: 'labelsmall',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],

          // Ek bilgiler
          if (exercise.notes != null || exercise.formattedRestTime.isNotEmpty) ...[
            const SizedBox(height: 8),
            if (exercise.notes != null) ...[
              ResponsiveText(
                '💡 ${exercise.notes!}',
                textType: 'labelsmall',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
            if (exercise.formattedRestTime.isNotEmpty) ...[
              const SizedBox(height: 2),
              ResponsiveText(
                '⏱️ Dinlenme: ${exercise.formattedRestTime}',
                textType: 'labelsmall',
                style: TextStyle(
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ],

          // Tamamlanma zamanı
          if (exercise.isCompleted && exercise.completedAt != null) ...[
            const SizedBox(height: 8),
            ResponsiveText(
              '✅ Tamamlandı: ${_formatTime(exercise.completedAt!)}',
              textType: 'labelsmall',
              style: TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Egzersiz tamamlama toggle - Gerçek API çağrısı
  Future<void> _toggleExerciseCompletion(WorkoutProgramExercise exercise) async {
    // Progress provider'dan mevcut session'ı kontrol et
    final progressState = ref.read(workoutProgressProvider);

    if (progressState.currentSession == null) {
      // Session yoksa önce session başlat
      await _startWorkoutSessionForExercise(exercise);
      return;
    }

    // Loading durumunu kontrol et
    if (progressState.isToggling) {
      LoggingService.info('Exercise toggle already in progress', tag: 'WORKOUT');
      return;
    }

    try {
      // Yeni tamamlama durumu (toggle)
      final newCompletionStatus = !exercise.isCompleted;

      LoggingService.info('Toggling exercise completion - exerciseID: ${exercise.workoutProgramExerciseID}, currentStatus: ${exercise.isCompleted}, newStatus: $newCompletionStatus',
        tag: 'WORKOUT');

      // API çağrısı yap
      await ref.read(workoutProgressProvider.notifier).toggleExerciseCompletion(
        exercise.workoutProgramExerciseID,
        newCompletionStatus,
      );

      // Başarılı olursa motivasyon mesajı göster
      final progressStateAfter = ref.read(workoutProgressProvider);
      if (!progressStateAfter.hasError) {
        _showMotivationMessage(newCompletionStatus);

        // Confetti efekti (tamamlandıysa)
        if (newCompletionStatus) {
          _showConfettiEffect();
        }
      } else {
        // Hata varsa hata mesajı göster
        _showErrorMessage(progressStateAfter.error ?? 'Bilinmeyen hata oluştu');
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramPage._toggleExerciseCompletion');
      _showErrorMessage('Egzersiz tamamlama durumu güncellenirken hata oluştu');
    }
  }

  /// Egzersiz için workout session başlat
  Future<void> _startWorkoutSessionForExercise(WorkoutProgramExercise exercise) async {
    try {
      // Kullanıcı bilgilerini al
      final user = ref.read(currentUserProvider);
      if (user == null) {
        _showErrorMessage('Kullanıcı bilgisi bulunamadı');
        return;
      }

      final userId = int.tryParse(user.nameidentifier);
      if (userId == null) {
        _showErrorMessage('Geçersiz kullanıcı ID');
        return;
      }

      // Member ID'yi al (bu bilgiyi workout program detail'den alacağız)
      final workoutState = ref.read(workoutProgramProvider);
      if (workoutState.selectedProgramDetail == null) {
        _showErrorMessage('Program detayları bulunamadı');
        return;
      }

      // İlk olarak member ID'yi bulmamız gerekiyor
      // Bu bilgiyi member service'den alacağız
      LoggingService.info('Starting workout session for exercise - userId: $userId, exerciseID: ${exercise.workoutProgramExerciseID}',
        tag: 'WORKOUT');

      _showInfoMessage('Antrenman oturumu başlatılıyor...');

      // TODO: Member ID'yi almak için member service çağrısı yapılacak
      // Şimdilik kullanıcıya bilgi mesajı gösterelim
      _showInfoMessage('Lütfen önce antrenman programı detaylarını açın');

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'WorkoutProgramPage._startWorkoutSessionForExercise');
      _showErrorMessage('Antrenman oturumu başlatılırken hata oluştu');
    }
  }

  /// Motivasyon mesajı göster
  void _showMotivationMessage(bool isCompleted) {
    final List<String> completionMessages = [
      'Harika! Bir adım daha yaklaştın! 💪',
      'Süpersin! Devam et! 🔥',
      'Mükemmel! Hedefine odaklan! 🎯',
      'Bravo! Sen bir şampiyon gibisin! 🏆',
      'İnanılmaz! Gücünü hissediyorum! ⚡',
      'Fantastik! Sınırlarını aştın! 🚀',
      'Muhteşem! Kararlılığın takdire şayan! 💎',
      'Efsane! Hiçbir şey seni durduramaz! 🦾',
    ];

    final List<String> uncompleteMessages = [
      'Sorun değil, tekrar deneyebilirsin! 😊',
      'Geri aldın, istediğin zaman tekrar yapabilirsin! 👍',
      'Esneklik de önemli, sen bilirsin! 🤗',
    ];

    // Random mesaj seç
    final messages = isCompleted ? completionMessages : uncompleteMessages;
    final random = DateTime.now().millisecondsSinceEpoch % messages.length;
    final message = messages[random];

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isCompleted ? Icons.celebration : Icons.undo,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ResponsiveText(
                message,
                textType: 'bodymedium',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: isCompleted
            ? Colors.green.shade600
            : Colors.orange.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Hata mesajı göster
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ResponsiveText(
                message,
                textType: 'bodymedium',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Bilgi mesajı göster
  void _showInfoMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.info_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ResponsiveText(
                message,
                textType: 'bodymedium',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue.shade600,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  /// Confetti efekti göster
  void _showConfettiEffect() {
    // TODO: Confetti animasyonu eklenebilir
    // Şimdilik basit bir haptic feedback
    // HapticFeedback.heavyImpact();
    LoggingService.info('Confetti effect triggered', tag: 'WORKOUT');
  }

  /// Zamanı formatla
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Az önce';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} dakika önce';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} saat önce';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}
